<template>
  <view class="p-icon-box" @click="clickEmit">
    <text
      class="p-icon"
      :class="`iconfont ` + name"
      :style="{
        color: disabled ? '#b9b9b9' : iconColor || '#6e6e6e',
        fontSize: size || '16px',
      }"
    />
    <text
      :style="{
        color: disabled ? '#b9b9b9' : color || '#000',
      }"
    >
      {{ title }}</text
    >
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
    size: {
      type: String,
      default: "",
    },
    color: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    iconColor: {
      type: String,
      default: "#6e6e6e",
    },
  },
  methods: {
    clickEmit() {
      if (!this.disabled) this.$emit("click");
    },
  },
};
</script>

<style lang="scss" scoped>
.p-icon {
  font-size: 16px;
}

.p-icon-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text {
    display: inline-block;
    margin-top: 5px;
    font-size: 12px;
    border: none;
    background-color: #fff;
    &::after {
      border: none;
    }
  }
}
</style>
