<script>
  import {
    ReceiptorderAPI,
    CoderulesproinfoAPI,
    BarcodeParsingAPI,
    DeliveryAPI,
    scanRecordAPI,
    getInfoMaterieAPI,
    RevokeAPI,
    TraceableAPI,
    finishedTraceableAPI
  } from "@/api/store/review";
  import { mapGetters } from "vuex";
  import navigationBar from "../../components/navigationBar.vue";
  import { useDict } from "@/utils/dict";

  export default {
    props: ["type", "condition", "pageNum"],
    computed: {
      ...mapGetters(["getInfo"]),
    },
    components: {
      navigationBar,
    },
    data() {
      return {
        form: { cpTraceCode: "", radiovalue1: "批次码" },
        column: [
          { label: "库存地点", prop: "inventoryLocation" },

          { label: "仓管人员", prop: "warehouseManager" },

          { label: "物料编码", prop: "materielCode" },
          { label: "物料描述", prop: "materielName" },

          { label: "单位", prop: "unit" },
          { label: "单据数量", prop: "receiptNum" },
          {
            label: "配送数量",
            prop: "sendNum",
            type: "自定义",
          },
        ],
        column2: [
          { label: "库存地点", prop: "inventoryLocation" },

          { label: "仓管人员", prop: "warehouseManager" },

          { label: "物料编码", prop: "materielCode" },
          { label: "物料描述", prop: "materielName" },

          { label: "单位", prop: "unit" },
          { label: "单据数量", prop: "receiptNum" },
          {
            label: "复核数量",
            prop: "checkNum",
            type: "自定义",
          },
        ],
        focus: false,
        query: { pageNum: 1, pageSize: 9999, type: 2 },
        datalist: [],
        datalist2: [],

        dataOldlist: [],
        //配送枚举
        deliverColumns: [
          {
            fieldName: "cpTraceCode",
            fieldDesc: "追溯码",
            width: 180,
            sorter: true,
            align: "center",
          },
          {
            fieldDesc: "复核数量",
            fieldName: "checkNum",
            type: "自定义",
          },
          { fieldName: "sendNum", fieldDesc: "配送数量", align: "center" },
        ],
        //撤销枚举
        revokeColumns2: [
          {
            fieldName: "cpTraceCode",
            fieldDesc: "追溯码",

            sorter: true,
            align: "center",
          },

          { fieldName: "revokeNum", fieldDesc: "撤销数量", align: "center" },
          { fieldName: "sendNum", fieldDesc: "配送数量", align: "center" },
        ],
        revokeColumns: [
          {
            fieldName: "traceableCode",
            fieldDesc: "追溯码",

            sorter: true,
            align: "center",
          },

          {
            fieldName: "revokeNum",
            fieldDesc: "撤销数量",
            align: "center",
          },
          {
            fieldName: "checkNum",
            fieldDesc: "复核数量",
            width: 80,
            align: "center",
          },
        ],
        value: 1,
        select: [],
        list1: [
          {
            name: "扫码入库",
            value: 1,
          },
          {
            name: "撤销入库",
            value: 2,
          },
          {
            name: "历史扫码",
            value: 3,
          },
        ],
        radiolist1: [
          {
            name: "批次码",
            disabled: false,
          },
          {
            name: "序列号",
            disabled: false,
          },
          {
            name: "集成码",
            disabled: false,
          },
        ],
        rules: {
          cpTraceCode: {
            type: "string",
            required: true,
            message: "请填写追溯码",
            trigger: ["blur", "change"],
          },
          checkNum: [
            {
              type: "string",
              required: true,
              message: "请填写复核数量",
              trigger: ["blur", "change"],
            },
            {
              // 自定义验证函数，见上说明
              validator: (rule, value, callback) => {
                // 上面有说，返回true表示校验通过，返回false表示不通过

                return Number(value) < 0 || Number(value) == 0
                  ? false
                  : Number(value) < this.form.sendNum ||
                    Number(value) == this.form.sendNum
                    ? true
                    : false;
              },
              message: "复核数量错误，不能小于0，不能大于配送数量",
              // 触发器可以同时用blur和change
              trigger: ["change", "blur"],
            },
          ],
          storageCode: {
            type: "string",
            required: true,
            message: "请填写库位",
            trigger: ["blur", "change"],
          },
          revokeNum: [
            {
              type: "string",
              required: true,
              message: "请填写撤销数量",
              trigger: ["blur", "change"],
            },
            {
              // 自定义验证函数，见上说明
              validator: (rule, value, callback) => {
                // 上面有说，返回true表示校验通过，返回false表示不通过

                return Number(value) < 0 || Number(value) == 0
                  ? false
                  : Number(value) < this.form.checkNum ||
                    Number(value) == this.form.checkNum
                    ? true
                    : false;
              },
              message: "撤销数量错误，不能小于0，不能大于配送数量",
              // 触发器可以同时用blur和change
              trigger: ["change", "blur"],
            },
          ],
        },
      };
    },
    /**
     * 下拉刷新
     */
    async onPullDownRefresh() {
      try {
        this.tableData = [];
        await this.getList();
      } catch (err) {
      } finally {
        uni.stopPullDownRefresh();
      }
    },
    /**
     * 页面上拉触底事件的处理函数
     */
    async onReachBottom() { },
    async onLoad(otip) {
      this.id = otip.id;
    },
    onShow() {
      this.screenComplete();
    },
    methods: {
      /**
       * 筛选重置
       */
      async screenReset() {
        await this.getList();
      },
      /**
       * 筛选完成
       */
      async screenComplete() {
        this.getList();
      },
      /**
       * 手动下拉
       */
      async scrolltolower() { },

      /**
       * tabs切换
       */
      tabsChane(v) {
        this.indixAcita = v.status;
        this.search();
      },
      /**偶数判断 */
      isEven(index) {
        return index % 2 === 0;
      },
      /**
       * 搜索
       */
      search(index) {
        this.getList();
      },

      /**清空 */
      clear(v) {
        this.query.itemId = "";

        this.search();
      },

      /**
       * 获取维保列表
       */
      async getList() {
        const { data: result } = await ReceiptorderAPI(this.id);
        /**
         * 解决页面不更新
         */
        this.$nextTick(async () => {
          this.form = result;
          this.form.radiovalue1 = "批次码";
          this.form.checkNum = this.value == 1 ? "" : this.form.checkNum;
          this.rules.checkNum.max = this.form.sendNum;
          console.log(this.rules.checkNum);
          this.getListOld();
          const { data: res } = await getInfoMaterieAPI(this.form.receiptNo);
          this.select = res.map((t) => ({ value: t.value, text: t.label }));
        });
      },

      /**扫码 */
      async scanCode() {
        let this_ = this;

        uni.scanCode({
          onlyFromCamera: true,
          success: async function (res) {
            // 扫描二维码，判断设备是否与维保工单绑定设备一致，否则提示“扫描失败！请认准设备扫描”回到上一页；
            if (res.scanType != "QR_CODE") {
              uni.showToast({
                title: "请正确扫描二维码",
              });
              return;
            }
            this_.codeValidate(res);
          },
        });
      },
      /**库位 */
      storageCode() {
        let this_ = this;
        uni.scanCode({
          onlyFromCamera: true,
          success: async function (res) {
            // 扫描二维码，判断设备是否与维保工单绑定设备一致，否则提示“扫描失败！请认准设备扫描”回到上一页；
            if (res.scanType != "QR_CODE") {
              uni.showToast({
                title: "请正确扫描二维码",
              });
              return;
            }
            this_.$set(this_.form, "storageCode", res.result);
          },
        });
      },
      /**追溯码解析校验 */
      async codeValidate(res) {
        try {
          let str = res ? res.result : this.form.cpTraceCode;
          console.log(str, "----");
          // let str = "0  let str = res.result;00000123456#khbm1234567890#401004#20250213#20250213BYZ";
          /**先判断物料编码是否一致 */
          const arr = str.split("#");
          console.log(arr[0] == this.form.materielCode, "ss");
          if (arr[0] != this.form.materielCode) {

            this.$refs.uToast.show({
              type: "error",
              icon: false,
              title: "失败主题",
              message: '当前追溯码没有对应的任务明细，请核对后重试',
            });
            return;
          }

          let obj = {
            materielCode: arr[0],
            custProCode: arr[1],
            code: str,
          };

          const {
            data: result
          } = await BarcodeParsingAPI(obj);

          //解析错误抛出异常
          if (!result.isOk) {

            this.$refs.uToast.show({
              type: "error",
              icon: false,
              title: "失败主题",
              message: result.msg,
            });
            return;
          }

          //校验客户编码和物料编码是否在产品信息表中存在
          const {
            data: val
          } = await CoderulesproinfoAPI({
            materielCode: result.data.WLMB,
            custProCode: result.data.KHBM,
            traceType: "PN",
            pageNum: 1,
            pageSize: 10,
          });
          //当匹配不出对应的数据的时候
          if (val.total == 0) {
            this.$refs.uToast.show({
              type: "error",
              icon: false,
              title: "失败主题",
              message: '物料编码与客户编码不匹配',
            });

          }
          //赋值，然后自动聚焦
          this.$set(this.form, "cpTraceCode", str);
          this.form.productionDate = result.data.SCRQ;
          this.form.batchNumber = result.data.PCH;
          this.form.materielCode = result.data.WLMB;
          this.value == 1 ? (this.focus = true) : (this.focusR = true);
        } catch (err) {
          console.log(err, "报错信息");
        }
      },

      /**确认配送 */
      async deliveryChange() {
        const res = await this.$refs.uForm.validate();
        if (!res) return;
        await DeliveryAPI(this.form);
        this.datalist.push({
          cpTraceCode: this.form.cpTraceCode,
          checkNum: this.form.checkNum,
        });
        this.form.traceableCode = this.form.cpTraceCode
        this.form.quantity = Number(this.form.checkNum)
        await finishedTraceableAPI(this.form)
        this.form.cpTraceCode = "";
        this.form.checkNum = "";
        this.storageCode.storageCode = "";
        this.getList();
        uni.showToast({
          title: "复核成功",
        });
      },

      /**撤销 */
      async revokeChange() {
        const res = await this.$refs.uForm.validate();
        if (!res) return;
        await RevokeAPI(this.form);

        const { data: result } = await TraceableAPI({
          traceableCode: this.form.cpTraceCode,
          itemId: this.form.itemId,
        });
        this.form.traceableCode = this.form.cpTraceCode
        this.form.quantity = - Number(this.form.revokeNum)
        await finishedTraceableAPI(this.form)
        this.datalist2.push({
          cpTraceCode: this.form.cpTraceCode,
          revokeNum: this.form.revokeNum,
          sendNum: result.sendNum - result.revokeNum,
        });
        this.g
        this.datalist2.push({
          cpTraceCode: this.form.cpTraceCode,
          revokeNum: this.form.revokeNum,
          sendNum: result.sendNum - result.revokeNum,
        });
        this.getList();
        uni.showToast({
          title: "撤销成功",
        });
      },
      /**
       * 跳转
       * @param {*} val
       */
      toPath(val) {
        if (val == 1) {
          uni.navigateBack({
            delta: 1,
          });
        } else {
          uni.switchTab({
            url: "/pages/index/index",
          });
        }
      },
      tabs(v) {
        this.value = v + 1;
        this.getList();
        console.log(value);
      },

      /*历史扫码*/
      async getListOld() {
        // this.query.itemId = this.form.itemId;
        this.query.receiptId = this.form.receiptId;
        const res = await scanRecordAPI(this.query);
        this.dataOldlist = res.rows;
      },
      getSummaries({ columns, data }) {
        console.log(columns, data, "data");
      },
    },

    /**
     * 监听从搜索页的搜索条件
     */
    watch: {},
  };
</script>

<template>
  <view class="pagex">
    <view>
      <u-navbar :title="`${
          value == 1
            ? '生产入库复核拣货'
            : value == 2
            ? '生产入库撤销配送'
            : '生产入库配送历史扫码'
        }`" @leftClick="toPath(1)" bgColor="#4669ea" placeholder>
      </u-navbar>
    </view>

    <p-card border class="info-box" v-if="value != 3">
      <template #header>
        <view class="flex-between">
          <view class="flex-items">
            <view class="ellipsis ellipsis-title">
              入库单号：{{ form.receiptNo }}
            </view>
          </view>
        </view>
      </template>
      <view class="content">
        <view class="list">
          <u-cell v-for="(item, index) in value == 1 ? column : column2" :key="index" :title="item.label + ':'"
            :border="false">
            <view slot="value" v-if="item.type != '自定义'" class="ellipsis-text">
              <text style="font-weight: 800">{{ form[item.prop] }}</text>
            </view>
            <view slot="value" v-else class="ellipsis-text">
              <text v-if="item.label == '配送数量'" style="color: red; font-weight: 800">{{ form[item.prop] }}</text>
              <text v-if="item.label == '复核数量'" style="color: red; font-weight: 800">{{ form[item.prop] }}</text>
            </view>
          </u-cell>
        </view>
      </view>
    </p-card>

    <!-- <u-tabs
      style="width: 68%; margin: 0 auto"
      :list="list1"
    
    ></u-tabs> -->
    <view class="lists">
      <view class="lists-item" :class="{ 'is-current': i + 1 == value }" v-for="(item, i) in list1" @click="tabs(i)"
        :key="i">{{ item.name }}
      </view>
    </view>

    <view v-if="value != 3">
      <u-radio-group v-model="form.radiovalue1" placement="row" style="width: 85%; margin: 10px auto">
        <u-radio :customStyle="{ marginBottom: '8px', marginRight: '15px' }" v-for="(item, index) in radiolist1"
          :key="index" :label="item.name" :name="item.name">
        </u-radio>
      </u-radio-group>

      <!-- 扫描入库 -->
      <u--form labelPosition="left" :model="form" v-if="value != 3" :rules="rules" :borderBottom="true" ref="uForm"
        labelWidth="80">
        <u-form-item label="库位" prop="storageCode" borderBottom ref="item1">
          <u-input v-model="form.storageCode" placeholder="请扫描产品放置库位">
            <!-- #ifndef APP-NVUE -->
            <u-icon name="scan" slot="suffix" class="#000" size="20" @click="storageCode"></u-icon>
            <!-- #endif -->
          </u-input>
        </u-form-item>
        <u-form-item label="追溯码" prop="cpTraceCode" borderBottom ref="item1">
          <u-input v-model="form.cpTraceCode" @confirm="codeValidate()" placeholder="请扫描入库产品追溯码">
            <!-- #ifndef APP-NVUE -->
            <u-icon name="scan" slot="prefix" :clearable="true" class="#000" size="20" @click="scanCode"></u-icon>
            <view slot="suffix" @click="form.cpTraceCode = ''">清空</view>
            <!-- #endif -->
          </u-input>
        </u-form-item>
        <u-form-item label="复核数量" prop="checkNum" borderBottom ref="item1" v-if="value == 1">
          <u--input type="number" v-model="form.checkNum" :focus="focus" placeholder="请输入追溯码对应配送数量"></u--input>
        </u-form-item>
        <u-form-item label="撤销数量" prop="revokeNum" borderBottom ref="item1" v-if="value == 2">
          <u--input type="number" v-model="form.revokeNum" placeholder="请输入撤销数量"></u--input>
        </u-form-item>
        <u-button :type="value == 1 ? 'success' : 'error'" style="width: 70%; margin: 10px auto"
          :text="value == 1 ? '复核确认' : '撤销确认'" @click="value == 1 ? deliveryChange() : revokeChange()"></u-button>
      </u--form>
      <basic-table :columns="value == 1 ? deliverColumns : revokeColumns2" :data="value == 1 ? datalist : datalist2"
        border :show-footer="value == 1" max-height="200px"></basic-table>
    </view>

    <!-- 历史扫码 -->
    <view v-else>
      <view class="search">
        <!-- <u-input
          placeholder="物料编码或者追溯码"
          style="margin: 0 10px"
          border="surround"
          v-model="query.materielCode"
          clearable
        >
        </u-input> -->
        <uni-data-select v-model="query.itemId" :localdata="select"></uni-data-select>
        <u-button style="width: 40px; height: 35px" type="success" text="搜索" @click="search"></u-button>
        <u-button style="width: 40px; height: 35px; margin-left: 5px" type="error" :plain="true" @click="clear"
          text="清空"></u-button>
      </view>
      <basic-table :columns="revokeColumns" :data="dataOldlist" border show-footer max-height="300px"></basic-table>
    </view>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<style lang="scss" scoped>
  .ellipsis-text {
    white-space: nowrap;
    // font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .flex-items {
    width: 180px;

    .title {
      margin-left: 10px;
    }
  }

  .lists {
    display: flex;
    justify-content: space-around;
    margin: 20rpx 0;
  }

  .is-current {
    color: #4669ea;
    border-bottom: 2px solid #4669ea;
  }

  .pagex {
    padding: 0 40rpx;
    background-color: #fff;
    min-height: 93.5vh;
    padding-bottom: 100rpx;
  }

  /deep/ .u-icon__icon {
    //   color: #fff !important;
  }

  .search {
    width: 93%;
    margin: 10rpx auto;
    display: flex;
  }

  /deep/ .u-navbar__content__title {
    color: #fff !important;
  }

  /deep/ .u-radio-group {
    flex: 0;
  }

  .titles {
    min-width: 100px;
    max-width: 170px;
    font-size: 16px;
    color: #333;
    font-weight: bold;
    margin: 0 5px;
  }

  .ellipsis-title {
    font-weight: 600;
  }

  ::v-deep .u-radio-group--row {
    justify-content: space-between;
  }

  .page {
    // height: calc(100vh - 0px);
    // background-color: #5375ff;

    // display: flex;
    // flex-direction: column;
  }

  .box {
    box-sizing: border-box;
    background: #f1f3f7;
    padding-top: 10rpx;
    // min-height: 100%;
    border-radius: 30rpx;
    flex: 1;
    overflow-y: auto;
    padding-bottom: 150rpx;
  }

  .receiptNo {
    background-color: #f1f3f7;
    padding: 15rpx;
    width: 90%;
    margin: 0 auto;
    border-radius: 15rpx;
    position: relative;
    font-weight: 700;
    margin-bottom: 5rpx;
  }

  .receiptNo::before {
    content: "";
    display: block;
    width: 3px;
    height: 68rpx;
    background-color: #4669ea;
    position: absolute;
    top: 0;
    left: 0;
  }

  .indixAcita {
    border: 1px solid #fff;
    padding: 20rpx;
    border-radius: 35rpx;
  }

  .atcie {
    color: #4669ea;
  }

  .search {
    width: 93%;
    margin: 10rpx auto;
    display: flex;
  }

  .search_item {
    height: 60rpx;
    flex: 1;

    color: #979797;
    text-align: center;
    line-height: 60rpx;
    cursor: pointer;
  }

  .activc {
    background-color: #2e8ff4;

    color: #fff;
  }

  .details {
    color: #2e8ff4;
  }

  .top {
    background-color: #5375ff;
  }

  /deep/ .u-navbar__content {
    // background-color: rgba(0, 0, 0, 0) !important;
  }

  /deep/ .u-cell__body {
    padding: 0px 10px !important;
    font-size: 12px;
  }

  /deep/ .u-cell__title {
    flex: 0.8;
  }

  /deep/ .u-cell__title-text {
    font-size: 12px;
  }

  .info-box {
    margin-top: 20px;
    position: relative;
    background-color: #f3f3f5;
  }
</style>