<script>
import {
  GetViewAPI,
  AddDataAPI,
  SubExamineAPI,
  SaveAddAPI,
} from '@/api/index/workOrder'

export default {
  data() {
    return {
      // 字典
      dictList: [
        {
          statusDict: '未通过',
        },
      ],
      rules: {
        actualInstructionName: {
          type: 'string',
          required: true,
          message: '请选择实际指令',
          trigger: ['blur', 'change'],
        },
      },
      id: '',
      //   数据载体
      dataObj: {
        status: 1,
        statusDict: '未通过',
        name: '片材机',
        address: `山不在高，有仙则名。水不在深，有龙则灵。斯是陋室，惟吾德馨。
				`,
        tel: '021-12345678',
        num: 10,
      },
      index: '',
      //   传入卡片的keys
      keys: [
        {
          label: '设备位置',
          prop: 'installationLocationName',
        },
        {
          label: '单据创建时间',
          prop: 'createTime',
        },
        {
          label: '设备编号',
          prop: 'deviceCode',
        },
        {
          label: '设备型号',
          prop: 'deviceUnitType',
        },
        {
          label: '项目类型',
          prop: 'projectType',
        },
      ],
      list: [],
      //   保养人员枚举
      upkeepKeys: [
        {
          label: '班组',
          prop: 'teamName',
        },
        {
          label: '维保负责人',
          prop: 'maintenanceManagerName',
        },
        {
          label: '维保人员',
          prop: 'maintenancePeople',
        },
      ],
      //   保养人员数据
      upkeepData: { name: '保养人员数据' },
      // 维保项目枚举
      maintenanceKeys: [
        {
          label: '项目名称',
          prop: 'maintenanceProjectName',
        },
        {
          label: '维保部位',
          prop: 'maintenancePosition',
        },
        {
          label: '计划指令',
          prop: 'paleInstruct',
        },
        {
          label: '维保内容',
          prop: 'maintenanceContent',
        },
        {
          label: '备件或耗材型号',
          prop: 'consumables',
        },
        {
          label: '保养数量',
          prop: 'name2',
        },
        {
          label: '实际指令',
          prop: 'name2',
        },
        {
          label: '备注',
          prop: 'name2',
        },
      ],
      // 维保项目数据
      maintenanceData: {
        name: '保养人员数据',
      },
      //指令
      instructShow: false,
      // 保存弹窗
      submitShow: false,
      // 提交审核弹窗
      subExamineShow: false,
      // 数据载体
      dataForm: {
        url: null,
      },
    }
  },
  onLoad(option) {
    this.id = option.id
    this.getData(option.id)
  },
  methods: {
    /**
     * 提交提示
     */
    async submitDic() {
      // 待维保和维保中的数据，可以做不必填，待提交和未通过需要做必填
      if (
        this.dataForm.status == '待提交' ||
        this.dataForm.status == '未通过'
      ) {
        const res = this.dataForm.list.every(
          (t) => t.actualInstruction.length > 0
        )

        if (!res) {
          uni.showToast({
            title: '实际指令不能为空',
            icon: 'none',
          })
          return
        }
        if (!this.dataForm.url || this.dataForm.url.length == 0) {
          uni.showToast({
            title: '维保照片不能为空',
            icon: 'none',
          })
          return
        }
        uni.showModal({
          title: '是否保存',
          content: '是否保存维保单据？取消后将不会保存维保信息。',
          success: ({ confirm }) => {
            if (confirm) this.submit()
          },
        })
      } else {
        uni.showModal({
          title: '是否保存',
          content: '是否保存维保单据？取消后将不会保存维保信息。',
          success: ({ confirm }) => {
            if (confirm) this.submit()
          },
        })
      }
    },
    /**
     * 提交审核提示
     */
    subExamineDic() {
      uni.showModal({
        title: '是否提交审核',
        content: '是否提交审核维保单据？',
        success: ({ confirm }) => {
          if (confirm) this.subExamine()
        },
      })
    },
    /**
     *保存维保单据
     */
    async submit() {
      console.log(this.dataForm, 'dataForm')

      try {
        const res = await SaveAddAPI({
          id: this.id,
          list: this.dataForm.list,
        })
        uni.navigateBack({
          delta: 1,
        })
        uni.showToast({
          title: '保存成功',
          icon: 'none',
        })
      } catch (err) {
        console.error(err)
      } finally {
      }
    },

    /**
     * 提交审核
     */
    async subExamine() {
      console.log(this.dataForm.list[0].actualInstruction.length, '--')
      const res = this.dataForm.list.every(
        (t) => t.actualInstruction.length > 0
      )

      if (!res) {
        uni.showToast({
          title: '实际指令不能为空',
          icon: 'none',
        })
        return
      }
      if (!this.dataForm.url || this.dataForm.url.length == 0) {
        uni.showToast({
          title: '维保照片不能为空',
          icon: 'none',
        })
        return
      }
      await SubExamineAPI({
        id: this.id,
        list: this.dataForm.list,
      })
      uni.navigateBack({
        delta: 1,
      })
      uni.showToast({
        title: '提交成功',
        icon: 'none',
      })
      // uni.showToast({
      //   title: '提交成功',
      //   icon: 'checkmark',
      //   success: () => (this.subExamineShow = false),
      // })
    },
    /**
     * 去添加
     */
    toAdd() {
      let this_ = this
      // 通过页面通讯获取上传完的数据
      uni.$once('images', function (e) {
        this_.$set(this_.dataForm, 'url', JSON.parse(e.result))
        // this_.imageTotal = e.total
      })
      console.log(this.dataForm.url)
      uni.navigateTo({
        url: `/pages/components/images?url=${JSON.stringify(
          this_.dataForm.url
        )}`,
      })
    },

    /**
     * 指令弹窗
     */
    instructDic(i, item) {
      this.index = i
      this.list = item.actualInstruction
      this.instructShow = true
    },
    /**
     * 获取详情
     */
    async getData(id) {
      const { data: result } = await GetViewAPI(id)
      this.dataForm = result
    },
    /**
     * 指令弹窗
     */
    async instructSub() {
      const result = await this.$refs.instruct.subMit()
      if (!result) return

      this.dataForm.list[this.index].actualInstructionName = result.teamUser
      this.dataForm.list[this.index].actualInstruction =
        result.actualInstruction
      this.instructShow = false
    },

    /**
     * 清空指令
     */
    clear(v) {
      v.actualInstruction = []
      v.actualInstructionName = ''
    },
  },
}
</script>
<template>
  <view>
    <p-card :keys="keys" :data="dataForm" border>
      <template #header>
        <view class="flex-items">
          <p-tag :text="dataForm.status" />
          <text class="title">{{ dataForm.deviceName }}</text>
        </view>
      </template>
    </p-card>

    <p-card class="mt-20" :padding="false">
      <u-cell
        arrow-direction="right"
        :border="false"
        :isLink="true"
        size="large"
        title="历史记录"
        @click="$u.navigateTo('/pages/components/examineHistory')"
      />
    </p-card>

    <view class="mt-20 vertical flex-items">
      <view class="vertical-line"></view>
      <view class="ml-10">保养人员</view>
    </view>
    <p-card allBorder class="mt-20" :keys="upkeepKeys" :data="dataForm" />

    <p-card class="mt-20" :padding="false">
      <!-- <u-cell
        arrow-direction="right"
        :border="false"
        :isLink="true"
        size="large"
        title="维保照片"
      >
        <view slot="title" class="u-slot-title">
          <text class="red">*</text>
          <text class="upkeepImg"
            >维保照片({{ dataForm.url ? dataForm.url.length : 0 }})</text
          >
        </view>

        <template #right-icon>
          <view class="toAdd" @click="toAdd">去添加</view>
        </template>
      </u-cell> -->
      <view class="image">
        <view class="u-slot-title">
          <text class="red">*</text>
          <text class="upkeepImg"
            >维保照片({{ dataForm.url ? dataForm.url.length : 0 }})</text
          >
        </view>
        <view class="toAdd" @click="toAdd">去添加</view>
      </view>
    </p-card>

    <!-- 维保项目 -->
    <view class="mt-20 vertical flex-items">
      <view class="vertical-line"></view>
      <view class="ml-10">保养项目</view>
    </view>

    <p-card v-for="(item, i) in dataForm.list" :key="i" allBorder class="mt-20"
      ><u--form :model="item" :rules="rules" ref="uForm">
        <u-form-item prop="disabled" borderBottom ref="item1">
          <text class="required">{{ item.maintenanceProjectName }}</text>
        </u-form-item>

        <u-form-item borderBottom ref="item1">
          <view><text class="disabled">维保部位：</text></view>
          <text>{{ item.maintenancePosition }}</text>
        </u-form-item>

        <u-form-item borderBottom ref="item1">
          <view><text class="disabled">计划指令：</text></view>
          <text>{{ item.paleInstruct }}</text>
        </u-form-item>
        <u-form-item borderBottom ref="item1">
          <view><text class="disabled">维保内容：</text></view>
          <text>{{ item.maintenanceContent }}</text>
        </u-form-item>
        <u-form-item borderBottom ref="item1">
          <view><text class="disabled">备件或耗材型号：</text></view>
          <text>{{ item.consumables }}</text>
        </u-form-item>
        <u-form-item borderBottom ref="item1">
          <view><text class="disabled">保养数量：</text></view>
          <text>{{ item.maintenanceNum }}</text>
        </u-form-item>
        <u-form-item prop="actualInstructionName" borderBottom ref="item1">
          <view
            ><text class="identifying">*</text
            ><text class="required">实际指令：</text></view
          >
          <u--input
            v-model="item.actualInstructionName"
            border="none"
            :clearable="true"
            @clear="clear(item)"
          ></u--input>
          <view class="toAdd" @click="instructDic(i, item)">指令</view>
        </u-form-item>
        <u-form-item borderBottom ref="item1">
          <view><text class="required">备注：</text></view>
          <u--textarea
            height="100"
            border="none"
            v-model="item.remark"
            placeholder="请输入内容"
          ></u--textarea>
        </u-form-item>
      </u--form>
    </p-card>

    <!-- 底部操作 -->
    <view class="bottom-av" />
    <!-- 底部操作 -->
    <view class="bottom">
      <u-button text="提交审核" @click="subExamineDic" />
      <u-button type="primary" text="保存" @click="submitDic" />
    </view>

    <u-modal
      showCancelButton
      :show="instructShow"
      title="实际指令"
      @confirm="instructSub"
      @cancel="instructShow = false"
    >
      <p-instruct ref="instruct" :list="list"></p-instruct>
    </u-modal>
  </view>
</template>

<style lang="scss" scoped>
.vertical {
  display: flex;
  margin: 0 20px;
  .vertical-line {
    background-color: #239ceb;
    width: 5px;
    height: 30px;
    border-radius: 10px;
  }
}
.image {
  display: flex;
  justify-content: space-between;
  padding: 10px;
}
.required {
  color: #02a7f0;
}

.identifying {
  color: red;
}
.disabled {
  color: #747474;
}
.upkeepImg {
  color: #239ceb;
}
.red {
  color: red;
}
.bottom-av {
  height: 50px;
}
.bottom {
  left: 0;
  bottom: 0;
  width: 100vw;
  display: flex;
  position: fixed;
  padding-bottom: 0;
  background-color: #fff;
  justify-content: space-around;
  border-top: 1px solid #000;

  .bottom-items {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    view {
      font-size: 12px;
    }
  }
}

.toAdd {
  padding: 5px;
  color: #3e91fd;
  border: 1px solid #3e91fd;
  font-size: 12px;
  border-radius: 5fr;
  background-color: #e9f5fd;
}
</style>
