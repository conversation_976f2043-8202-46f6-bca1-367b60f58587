import request from "@/utils/request"
/**
 * 获取设备维修列表
 * @returns {object} params 查询条件
 */
export const GetInitAPI = (params) => {
    return request({
        url: "/api/notifi/init",
        method: "GET",
        params,
    })
}

/**下发 */
export const SubmitSendAPI = (data) => {
    return request({
        url: "/api/notifi/send",
        method: "POST",
        data,
    })
}


/**
 * 树结构
 */

export const GetTreeSelectAPI = () => {
    return request({
        url: "/api/system/dept/treeSelect",
        method: "GET",

    })
}

/*角色 */

export const GetRoleAPI = () => {
    return request({
        url: "/api/system/role/list",
        method: "GET",

    })
}
