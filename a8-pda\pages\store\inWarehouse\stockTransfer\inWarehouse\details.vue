<template>
	<z-paging ref="paging" v-model="dataList" @query="query" :auto="false" @virtualListChange="virtualListChange"
		use-virtual-list :force-close-inner-list="true" cell-height-mode="dynamic">

		<template #top>
			<view>
				<u-navbar title="调拨入库任务详情" @leftClick="toPath()" bgColor="#4669ea" placeholder></u-navbar>
			</view>
			<view style="display: flex;gap: 10rpx;align-items: center;margin: 10rpx;">
				<uni-easyinput style="flex: 4;" v-model="queryData.queryText" placeholder="录入物料编码或追溯码搜索"
					border="surround" prefixIcon="scan" @iconClick="scanCode" @clear="clear">
				</uni-easyinput>

				<u-button style="flex: 1;" size="small" type="success" text="搜索" @click="search"></u-button>
			</view>

			<uni-section :title="'调拨单号:' + rowData.transferOrderNo" type="line"
				style="border-radius: 20rpx; margin-left: 10rpx; margin-right: 10rpx;"></uni-section>

		</template>

		<view :id="`zp-id-${item.zp_index}`" :key="item.zp_index" v-for="(item, index) in virtualList">
			<uni-card style="border-radius: 20rpx">

				<view style="display: flex;justify-content: space-between">
					<text>物料编码: </text>
					<text>{{item.materielCode}}</text>
				</view>
				<view style="display: flex;justify-content: space-between">
					<text>物料描述: </text>
					<text>{{item.materielName}}</text>
				</view>
				<view style="display: flex;justify-content: space-between">
					<text>调出仓库: </text>
					<text>{{item.location}}</text>
				</view>
				<view style="display: flex;justify-content: space-between">
					<text>调入仓库: </text>
					<text>{{item.receiveLocation}}</text>
				</view>
				<view style="display: flex;justify-content: space-between">
					<text>单位: </text>
					<text>{{item.unit}}</text>
				</view>
				<view style="display: flex;justify-content: space-between">
					<text>单据数量: </text>
					<text>{{item.qty}}</text>
				</view>
				<view style="display: flex;justify-content: space-between">
					<text>待出库数量: </text>
					<text>{{item.qty - item.scannedQuantity}}</text>
				</view>
				<view
					style="display: flex;justify-content: center;align-items: center;margin-top: 5px;margin-left: 5px">
					<u-button :customStyle="btnCustomStyle" type="primary" text="入库" @click="details(item)"></u-button>
				</view>

			</uni-card>
		</view>
	</z-paging>
</template>

<script>
	import {
		GetTransferInDetailsAPI
	} from "@/api/store/inWarehouse/inWarehouse"
	export default {
		data() {
			return {
				dataList: [],
				queryData: {},
				virtualList: [],
				btnCustomStyle: {
					'margin-bottom': '15rpx',
					width: '50px',
					height: '30px',
					marginRight: '0'
				},
				rowData: {},
			};
		},
		onLoad: function(option) {
			const that = this;
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.on("rowData", function(data) {
				that.rowData = data;
			});
		},
		watch: {
			rowData: {
				handler: function(val, oldVal) {
					this.$refs.paging.reload();
				},
				deep: true
			}
		},
		methods: {
			search() {
				if (this.queryData.queryText) {
					let searchText = this.queryData.queryText;
					let filteredList = [];

					// 判断是否包含#
					if (searchText.includes('#')) {
						// 取第一个#前面的字符串
						searchText = searchText.split('#')[0];
					}

					// 筛选匹配materielCode的数据
					filteredList = this.virtualList.filter(item =>
						item.materielCode === searchText
					);

					// 更新列表数据
					this.virtualList = filteredList;
					this.$refs.paging.complete(filteredList);
				} else {
					// 如果搜索文本为空，重新加载所有数据
					this.$refs.paging.reload();
				}
			},
			clear() {
				this.queryData.queryText = ''; // 清空搜索框
				this.$refs.paging.reload(); // 重新加载所有数据
			},
			scanCode() {
				const that = this
				uni.scanCode({
					onlyFromCamera: true,
					success: function(res) {
						// 扫描结果赋值给queryText
						that.$set(that.queryData, "queryText", res.result);
						// 扫描成功后执行搜索
						that.search();
					},
					fail() {
						that.$refs.toastRef.show({
							type: "error",
							message: "识别失败，请重新识别。"
						})
					}
				})
			},
			query() {
				GetTransferInDetailsAPI(this.rowData.transferOrderNo).then(res => {
					console.log(this.rowData.transferOrderNo);
					// 过滤掉待出库数量为0的数据
					const filteredRows = res.rows.filter(item => {
						const remainingQty = parseFloat(item.qty) - parseFloat(item.scannedQuantity);
						return remainingQty > 0;
					});

					this.virtualList = filteredRows;
					this.$refs.paging.complete(filteredRows);
				}).catch(res => {
					this.$refs.paging.complete(false);
				})
			},
			virtualListChange(vList) {
				this.virtualList = vList;
			},
			//返回
			toPath() {
				uni.navigateBack({
					delta: 1
				})
			},
			details(row) {
				const allData = Object.assign(row, this.rowData)
				uni.navigateTo({
					url: `/pages/store/inWarehouse/stockTransfer/inWarehouse/transfer`,
					success: function(res) {
						res.eventChannel.emit('item', allData)
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.uni-body {
		font-size: 14px;
		font-weight: 400;
		line-height: 22px
	}

	.card-body-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 15rpx;
		font-family: 'PingFang-SC-Light';
	}

	.search {
		width: 93%;
		margin: 10rpx 10rpx 10rpx 12rpx;
		display: flex;
		flex-direction: column;
		gap: 15rpx;
		background-color: #ffffff;
		padding: 15rpx;
		border-radius: 20rpx;
		box-shadow: 0 10rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.search-row {
		display: flex;
		gap: 10rpx;
	}
</style>