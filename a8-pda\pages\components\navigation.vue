<template>
  <view>
    <!-- 状态栏占位 -->
    <view :style="{ paddingTop: statusBarHeight + 'px' }"></view>
    <!-- 导航栏内容 -->
    <view
      :style="{ height: navBarHeight + 'px', backgroundColor: background }"
      class="navBarComponent"
    >
      <view class="navContent">
        <slot></slot>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "navigationBar",
  data() {
    return {
      statusBarHeight: 0,
      navBarHeight: 0,
      count: 0,
      icons: [],
    };
  },
  props: {
    titles: String,
    links: Array,
    background: String,
  },
  methods: {
    toPage(url) {
      uni.navigateTo({
        url: "/pages/index/message/index",
      });
    },
  },
  created() {
    // 动态获取手机状态栏高度（电量显示区域），H5没有状态栏，会为0
    this.statusBarHeight = uni.getSystemInfoSync()["statusBarHeight"];
    // #ifdef H5
    this.navBarHeight = 45;
    // #endif
    // #ifndef H5
    this.navBarHeight = this.statusBarHeight + 11;
    // #endif

    if (typeof this.$props.links === "undefined") {
      this.count = 0;
    } else {
      this.count = this.$props.links.length;
      this.icons = this.$props.links;
    }
  },
};
</script>

<style lang="scss">
.navBarComponent {
  display: flex;
  align-items: center;
  box-sizing: border-box;

  .navContent {
    position: relative;
    display: flex;
    align-items: center;
    padding-left: 15px;
    padding-right: 15px;
    height: 100%;
    width: 90%;

    .title {
      font-size: 16px;
      //   flex: 1;
      position: absolute;
      left: 50%;
      top: 50%;
      color: #fff;
      text-align: center;
      transform: translate(-50%, -50%);
    }

    .badge {
      position: absolute;
      right: 5%;
    }
  }
}
</style>