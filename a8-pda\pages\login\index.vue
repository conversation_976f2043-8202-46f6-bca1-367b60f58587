<template>
  <view class="pages">
    <view class="login-main">
      <view class="logo-img">
        <u-avatar :src="icon" shape="square" size="90"></u-avatar>
      </view>
      <view class="login-hi">Hi~</view>

      <view class="login-title">欢迎登录制造运营MOM管理系统</view>
      <view class="login-box">
        <u-form class="formBox" labelPosition="left" :model="submitForm" :rules="rules" ref="uForm">
          <u-form-item prop="username">
            <u-input type="string" border="bottom" style="width: 100%" placeholder="请输入登陆账号"
              v-model="submitForm.username">
              <template slot="prefix">
                <u-icon name="account" color="#dbdbdb" size="28"></u-icon>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item prop="password">
            <u-input :type="type ? 'password' : ''" border="bottom" :password-icon="true" placeholder="请输入登录密码"
              v-model="submitForm.password">
              <template slot="prefix">
                <u-icon name="lock-open" color="#dbdbdb" size="28"></u-icon>
              </template>
              <template slot="suffix">
                <u-icon name="eye-fill" v-if="type" @click="type = !type" color="#dbdbdb" size="20"></u-icon>
                <u-icon name="eye-off" v-else @click="type = !type" color="#dbdbdb" size="20"></u-icon>
              </template>
            </u-input>
          </u-form-item>
          <u-button type="primary" shape="circle" color="#4f71ff" size="large" text="登录" class="button"
            :loading="btnLoading" @click="login" />
        </u-form>
      </view>
    </view>
    <view class="forgot_password" @click="forgotPassword()">忘记密码</view>
    <view class="url_info" @click="showUrl = true">
      <u-icon name="setting-fill" color="#111112" size="28"></u-icon>
    </view>

    <u-toast ref="uToast"></u-toast>
    <u-picker :show="showUrl" :columns="columns" @cancel="showUrl = false" @confirm="submitUrl"></u-picker>
  </view>
</template>

<script>
  import { setURL, getURL } from "@/utils/auth.js";
  export default {
    data() {
      return {
        // 加载状态
        btnLoading: false,

        submitForm: {
          username: "",
          password: "",
        },
        showUrl: false, //控制地址显示
        columns: [
          [
            "http://**************:8080",
            "http://**************:8080",
            "http://***********:10010",
            "http://*************:8080",
          ],
        ],
        type: true, //密码类型
        connectStatus: "未链接",
        registrationID: "未获得",
        icon: "../../static/images/logo.png",
        rules: {
          username: [
            {
              type: "string",
              required: true,
              message: "请输入登陆账号",
              trigger: ["blur", "change"],
            },
          ],

          password: [
            {
              type: "string",
              required: true,
              message: "请输入登录密码",
              trigger: ["blur", "change"],
            },
            {
              min: 6,
              max: 12,
              message: "长度在6-8个字符之间",
            },
          ],
        },
      };
    },
    methods: {
      /**
       * 登录
       */
      async login() {
        try {
          await this.$refs.uForm.validate();
          await this.$store.dispatch("login", this.submitForm);
          uni.switchTab({
            url: "/pages/index/index",
          });
        } catch (err) {
          console.log(err);
        } finally {
          this.btnLoading = false;
        }
      },
      /**忘记密码 */
      forgotPassword() {
        this.$refs.uToast.show({
          type: "error",
          title: "默认主题",
          message: "请联系系统管理员协助处理！",
          duration: 2000,
        });
      },

      /**确定环境选择 */

      submitUrl(url) {
        console.log(url.value[0], "url");
        setURL(url.value[0]);
        this.showUrl = false;
      },
    },

    //
  };
</script>

<style lang="scss" scoped>
  .pages {
    /* display: flex;
    flex-direction: column;
    justify-content: space-around; */
    box-sizing: border-box;
    align-items: center;
    background-image: url("/static/images/loging_bg.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    height: 100%;
    /* padding: 0px 64rpx; */
    /* padding-top: 283rpx; */
    position: relative;
  }

  .url_info {
    position: absolute;
    right: 5%;
    bottom: 5%;
  }

  .login-main {
    width: 622rpx;
    height: 644rpx;
    position: absolute;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
  }

  .logo-img {
    display: flex;
    justify-content: space-around;
    margin-bottom: 50rpx;
  }

  .login-hi {
    /* margin-top: 283rpx; */
    width: 67rpx;
    height: 35rpx;
    font-family: SF Pro Display;
    font-weight: 500;
    font-size: 48rpx;
    color: #ffffff;

    line-height: 68rpx;
  }

  .login-title {
    margin-top: 28rpx;
    margin-bottom: 36rpx;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 35rpx;
    color: #ffffff;
  }

  .button {
    margin-top: 50rpx;
    height: 80rpx;
  }

  .login-box {
    width: 100%;
    height: 550rpx;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 30px;
    padding: 71rpx 32rpx;
  }

  .forgot_password {
    left: 50%;
    color: red;
    position: absolute;
    bottom: 5%;
    transform: translateX(-50%);
  }

  .user_box {
    width: 140px;
    height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding: 30px;
    border-radius: 100%;
    border: 1px solid #898989;
    margin-bottom: 20px;
  }

  .user {
    width: 100%;
    height: 100%;
  }

  .formBox {
    // width: 300px;
  }
</style>