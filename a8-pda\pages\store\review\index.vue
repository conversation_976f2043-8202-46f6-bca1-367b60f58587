<script>
import { GetListAPI, toSAPAPI } from "@/api/store/review";
import { GetListSapAPI } from "@/api/store/produce";

import { mapGetters } from "vuex";
import navigationBar from "../../components/navigationBar.vue";
import { useDict } from "@/utils/dict";

export default {
  props: ["type", "condition", "pageNum"],
  computed: {
    ...mapGetters(["getInfo"]),
  },
  components: {
    navigationBar,
  },
  data() {
    return {
      device_maintence_status: [],

      column: [
        { label: "库存地点", prop: "inventoryLocation" },
        { label: "入库人员", prop: "receipterUser" },
        { label: "单据数量", prop: "receiptNum", type: "自定义" },
        {
          label: "配送数量",
          prop: "sendNum",
          type: "自定义",
        },
      ],

      // 分页查询
      query: {
        pageNum: 1,
        pageSize: 5,
        source: 1,
      },
      // 分页查询
      querySap: {
        pageNum: 1,
        pageSize: 5,
        source: 2,
      },
      active: 1,
      // 加载状态
      status: "loadng",
      statusSap: "loadng",
      //   列表数据
      tableData: [],
      tableDataSap: [],
      total: 0,
      totalSap: 0,
      // 字典
      dicts: {},
      // 筛选弹窗
      screenShow: false,
      // 提交审核告警弹窗
      show: false,

      // 派工人列表
      workList: [],
    };
  },
  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      if (this.active == 0) {
        this.tableData = [];
        this.status = "loading";
        this.query.pageNum = 1;
        await this.getList();
      } else {
        this.tableDataSap = [];
        this.statusSap = "loading";
        this.querySap.pageNum = 1;
        await this.getListSap();
      }
    } catch (err) {
    } finally {
      uni.stopPullDownRefresh();
    }
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  async onReachBottom() {},
  async onLoad() {
    // const { data: res } = await GetStatusAPI();
    // this.list = res;
  },
  onShow() {
    this.screenComplete();
    this.tableDataSap = [];
    this.querySap.pageNum = 1;
    (this.statusSap = "loadng"), this.getListSap();
    this.dict();
  },
  methods: {
    /**获取字典 */
    async dict() {
      this.device_maintence_status = await useDict("device_maintence_status");
    },
    /**
     * 筛选重置
     */
    async screenReset() {
      this.screenShow = false;
      this.query.mineType = 0;
      this.tableData = [];
      this.query.pageNum = 1;
      this.status = "loading";
      await this.getList();
    },
    /**
     * 筛选完成
     */
    async screenComplete() {
      this.query.pageNum = 1;
      this.status = "loading";
      this.tableData = [];

      this.getList();
    },
    /**
     * 手动下拉
     */
    async scrolltolower() {
      //根据active判断当前任务来源
      if (this.active == 0) {
        if (this.status == "nomore") return;
        this.status = "loading";
        this.query.pageNum = ++this.query.pageNum;
        await this.getList();
      } else {
        if (this.statusSap == "nomore") return;
        this.statusSap = "loading";
        this.querySap.pageNum = ++this.querySap.pageNum;
        await this.getListSap();
      }
    },
    /**扫码 */
    scanCode() {
      let this_ = this;

      uni.scanCode({
        onlyFromCamera: true,
        success: async function (res) {
          // 扫描二维码，判断设备是否与维保工单绑定设备一致，否则提示“扫描失败！请认准设备扫描”回到上一页；
          if (res.scanType != "QR_CODE")
            uni.showToast({
              title: "请正确扫描二维码",
            });
          this_.$set(this_.query, "receiptNo", res.result);
        },
      });
    },
    /**
     * tabs切换
     */
    tabsChane(v) {
      this.indixAcita = v.status;
      this.query.status = v.status;

      this.search();
    },
    /**偶数判断 */
    isEven(index) {
      return index % 2 === 0;
    },
    /**
     * 搜索
     */
    search(index) {
      this.query.pageNum = 1;
      this.query.pageSize = 5;
      this.status = "loading";
      this.tableData = [];
      this.getList();
    },
    searchSap(index) {
      console.log("s");
      this.querySap.pageNum = 1;
      this.querySap.pageSize = 5;
      this.statusSap = "loading";
      this.tableDataSap = [];
      this.getListSap();
    },

    clear(v) {
      this.query.receipterUser = "";
      this.query.receiptNo = "";
      this.querySap.receipterUser = "";
      this.querySap.receiptNo = "";
      this.search();
      this.searchSap();
    },
    /**
     * 获取维保列表
     */
    async getList() {
      const result = await GetListAPI(this.query);
      /**
       * 解决页面不更新
       */
      this.$nextTick(() => {
        this.tableData = this.tableData.concat(result.rows);
        this.total = result.total;
        if (this.tableData.length >= this.total) {
          this.status = "nomore";
        }
      });
    },
    /**
     * 获取SAP列表
     */
    async getListSap() {
      console.log(this.querySap, "this.querySap");
      const result = await GetListAPI(this.querySap);
      /**
       * 解决页面不更新
       */
      this.$nextTick(() => {
        this.tableDataSap = this.tableDataSap.concat(result.rows);
        this.totalSap = result.total;
        console.log(
          this.tableDataSap.length >= this.totalSap,
          "this.tableDataSap.length"
        );
        if (this.tableDataSap.length >= this.totalSap) {
          this.statusSap = "nomore";
        }
      });
    },
    /**同步erp */
    async getToSAPAPI(receiptNo) {
      await toSAPAPI({ receiptNo: receiptNo });
      uni.showToast({
        title: "同步成功",
      });
    },
    /**
     * 跳转
     * @param {*} val
     */
    toPath(val) {
      console.log("v", val);
      if (val == 1) {
        uni.navigateBack({
          delta: 1,
        });
      } else {
        uni.switchTab({
          url: "/pages/index/index",
        });
      }
    },
    open() {
      this.$refs.calendar.open();
    },

    /**跳转详情 */
    toView(t) {
      try {
        //监管人员只能查看

        uni.navigateTo({
          url: "/pages/store/review/view?id=" + t.receiptId,
        });
      } catch (err) {}
    },
    /**跳转详情 */
    toViewSap(t) {
      try {
        //监管人员只能查看

        uni.navigateTo({
          url: "/pages/store/review/viewSap?id=" + t.receiptNo,
        });
      } catch (err) {}
    },
  },

  /**
   * 监听从搜索页的搜索条件
   */
  watch: {},
};
</script>

<template>
  <view class="page">
    <view>
      <u-navbar
        title="生产入库复核任务"
        @leftClick="toPath(1)"
        bgColor="#4669ea"
        placeholder
      >
      </u-navbar>
    </view>
    <view class="tabs">
      <ms-tabs
        :list="[
          {
            title: '生产入库自建系统',
          },
          {
            title: 'ERP同步',
          },
        ]"
        v-model="active"
      ></ms-tabs>
    </view>
    <view class="search" v-if="active == 0">
      <u-input
        placeholder="入库单号"
        border="surround"
        v-model="query.receiptNo"
        clearable
      >
        <u-icon
          name="scan"
          slot="prefix"
          class="#000"
          size="20"
          @click="scanCode"
        ></u-icon>
      </u-input>
      <u--input
        placeholder="入库人员"
        style="margin: 0 10px"
        border="surround"
        v-model="query.receipterUser"
        clearable
      >
      </u--input>
      <u-button
        style="width: 40px; height: 35px"
        type="success"
        text="搜索"
        @click="search"
      ></u-button>
      <u-button
        style="width: 40px; height: 35px; margin-left: 5px"
        type="error"
        :plain="true"
        @click="clear"
        text="清空"
      >
      </u-button>
    </view>
    <view class="search" v-else>
      <u-input
        placeholder="入库单号"
        border="surround"
        v-model="querySap.receiptNo"
        clearable
      >
        <u-icon
          name="scan"
          slot="prefix"
          class="#000"
          size="20"
          @click="scanCode"
        ></u-icon>
      </u-input>
      <u--input
        placeholder="入库人员"
        style="margin: 0 10px"
        border="surround"
        v-model="querySap.receipterUser"
        clearable
      >
      </u--input>
      <u-button
        style="width: 40px; height: 35px"
        type="success"
        text="搜索"
        @click="searchSap"
      ></u-button>
      <u-button
        style="width: 40px; height: 35px; margin-left: 5px"
        type="error"
        :plain="true"
        @click="clear"
        text="清空"
      >
      </u-button>
    </view>
    <view class="box">
      <u-list
        @scrolltolower="scrolltolower"
        v-if="active == 0 && tableData.length != 0"
      >
        <u-list-item v-for="(t, i) in tableData" :key="i">
          <p-card border class="info-box" v-if="t.documentStatus != 5">
            <template #header>
              <view class="flex-between">
                <view class="flex-items">
                  <view
                    class="ellipsis ellipsis-title"
                    :class="{ atcie: isEven(i + 1) }"
                  >
                    入库单号：{{ t.receiptNo }}
                  </view>
                </view>

                <!-- <u-tag text="同步erp" type="error"></u-tag> -->
                <view class="details" @click="toView(t)">详情</view>
              </view>
            </template>

            <view class="content">
              <view class="list">
                <u-cell
                  v-for="(item, index) in column"
                  :key="index"
                  :title="item.label + ':'"
                  :border="false"
                >
                  <text
                    slot="value"
                    class="ellipsis-text"
                    v-if="item.type != '自定义'"
                    >{{ t[item.prop] }}</text
                  >
                  <view slot="value" v-else class="ellipsis-text">
                    <text v-if="item.prop == 'status'">{{
                      dictText(t.status, device_maintence_status)
                    }}</text>
                    <text
                      v-if="item.label == '配送数量'"
                      style="color: red; font-weight: 800"
                      >{{ t[item.prop] }}</text
                    >
                    <text
                      v-if="item.label == '单据数量'"
                      style="font-weight: 800"
                      >{{ t[item.prop] }}</text
                    >
                  </view>
                </u-cell>

                <view></view>
              </view>
            </view>
          </p-card>
        </u-list-item>
        <u-loadmore :status="status" />
      </u-list>

      <u-list
        @scrolltolower="scrolltolower"
        v-if="active == 1 && tableDataSap.length != 0"
      >
        <u-list-item v-for="(t, i) in tableDataSap" :key="i">
          <p-card border class="info-box" v-if="t.documentStatus != 5">
            <template #header>
              <view class="flex-between">
                <view class="flex-items">
                  <view
                    class="ellipsis ellipsis-title"
                    :class="{ atcie: isEven(i + 1) }"
                  >
                    入库单号：{{ t.receiptNo }}
                  </view>
                </view>
                <u-button
                  type="error"
                  text="同步erp"
                  style="width: 50px; margin-right: 10px"
                  @click="getToSAPAPI(t.receiptNo)"
                  :disabled="t.checkNum != t.receiptNum"
                  size="mini"
                ></u-button>
                <!-- <u-tag text="同步erp" type="error"></u-tag> -->
                <view class="details" @click="toViewSap(t)">详情</view>
              </view>
            </template>

            <view class="content">
              <view class="list">
                <u-cell
                  v-for="(item, index) in column"
                  :key="index"
                  :title="item.label + ':'"
                  :border="false"
                >
                  <text
                    slot="value"
                    class="ellipsis-text"
                    v-if="item.type != '自定义'"
                    >{{ t[item.prop] }}</text
                  >
                  <view slot="value" v-else class="ellipsis-text">
                    <text v-if="item.prop == 'status'">{{
                      dictText(t.status, device_maintence_status)
                    }}</text>
                    <text
                      v-if="item.label == '配送数量'"
                      style="color: red; font-weight: 800"
                      >{{ t[item.prop] }}</text
                    >
                    <text
                      v-if="item.label == '单据数量'"
                      style="font-weight: 800"
                      >{{ t[item.prop] }}</text
                    >
                  </view>
                </u-cell>

                <view></view>
              </view>
            </view>
          </p-card>
        </u-list-item>
        <u-loadmore :status="statusSap" />
      </u-list>
      <u-empty
        mode="data"
        class="empty"
        v-if="tableData.length == 0 && active == 0"
      >
      </u-empty>
      <u-empty
        mode="data"
        class="empty"
        v-if="tableDataSap.length == 0 && active == 1"
      >
      </u-empty>
    </view>
    <uni-calendar
      ref="calendar"
      class="uni-calendar--hook"
      :clear-date="true"
      :insert="false"
      :lunar="false"
      :range="true"
      @confirm="confirm"
      @close="close"
    />
  </view>
</template>
<style lang="scss" scoped>
.ellipsis-text {
  white-space: nowrap;
  // font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.empty {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.flex-items {
  flex: 1;

  .title {
    margin-left: 10px;
  }
}

// /deep/ .u-icon__icon {
//   color: #fff !important;
// }

/deep/ .u-navbar__content__title {
  color: #fff !important;
}

.list {
}

.titles {
  min-width: 100px;
  max-width: 170px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin: 0 5px;
}

.ellipsis-title {
  font-weight: 600;
  flex: 1;
}

.page {
  height: calc(100vh - 0px);
  background-color: #5375ff;

  display: flex;
  flex-direction: column;
}

.box {
  box-sizing: border-box;
  background: #f1f3f7;
  padding-top: 10rpx;
  // min-height: 100%;
  border-radius: 30rpx;
  flex: 1;
  overflow-y: auto;
  /* padding-bottom: 150rpx; */
}

.indixAcita {
  border: 1px solid #fff;
  border-radius: 35rpx;
}

.atcie {
  color: #4669ea;
}

.search {
  width: 93%;
  margin: 10rpx auto;
  display: flex;
}

.search_item {
  height: 60rpx;
  flex: 1;

  color: #979797;
  text-align: center;
  line-height: 60rpx;
  cursor: pointer;
}

.activc {
  background-color: #2e8ff4;

  color: #fff;
}

.details {
  color: #2e8ff4;
}

// .search_item:nth-of-type(2) {
//   border-left: 1px solid #979797;
//   border-right: 1px solid #979797;
// }
.bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  height: 150rpx;
  width: 100%;
  border-top: 1px solid #747474;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tabs {
  height: 100rpx;
  display: flex;
  justify-content: space-evenly;
  background-color: #5375ff;
  align-items: center;

  .tabs-list {
    color: #fff;
    padding: 1rpx 0rpx;
    width: 17%;
    display: flex;
    font-size: 25rpx;
    align-items: center;
    height: 50%;
    justify-content: center;
    position: relative;

    .badge {
      top: 0;
      position: absolute;
      right: -10%;
    }
  }
}

.top {
  background-color: #5375ff;
}

/deep/ .u-navbar__content {
  // background-color: rgba(0, 0, 0, 0) !important;
}

/deep/ .u-cell__body {
  padding: 0px 10px !important;
  font-size: 12px;
}

/deep/ .u-cell__title {
  flex: 0.8;
}

/deep/ .u-cell__title-text {
  font-size: 12px;
}

.info-box {
  margin-top: 20px;
  position: relative;
}

.info-box::before {
  content: "";
  display: block;
  width: 3px;
  height: 60rpx;
  background-color: #4669ea;
  position: absolute;
  top: 0;
  left: 0;
}

.content {
  width: 100%;
  margin-top: 5px;

  .content-item {
    display: flex;
    align-items: center;
    margin-left: 10px;
    width: 100%;

    .label {
      color: #747474;
    }
  }
}
</style>