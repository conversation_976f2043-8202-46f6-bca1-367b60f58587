import request from "../utils/request";
/**
 *登录
 */
export const GetLoginAPI = (data) => {
    return request({
        url: "/auth/login",
        method: "POST",
        isEncrypt: true,
        data,
    });
};

/**
 * 获取用户信息
 * @returns
 */
export const GetUseInfoAPI = () => {
    return request({
        url: "/system/user/getInfo",
        method: "GET",
    });
};

/**
 * 退出登录
 * @param {*} data
 * @returns
 */
export const LoginOutAPI = (data) => {
    return request({
        url: "/auth/logout",
        method: "post",
        data,
    });
};

/**
 * 字典
 * @param {*} data
 * @returns
 */
export const dataTypeAPI = (data) => {
    return request({
        url: "/system/dict/data/type/" + data,
        method: "GET",
    });
};

/**
 * 审核历史
 * @param auditDoc 唯一字段
 * @param  perms 权限码
 * @returns
 */
export function GetHistoryAPI(params) {
    return request({
        url: "/pureut-app/repair/auditHistory/" + params,
        method: "GET",
    });
}

/**

/**
 * 指令
 */
export function GetInstructDdlAPI() {
    return request({
        url: "/app/maintenanceWorkOrder/instructDdl",
        method: "GET",
    });
}
/**
 * 根据班组id获取班组成员
 * @param {string} id 班组id
 */
export function GetTeamMemberAPI(id) {
    return request({
        url: "/pureut-app/team/getTeamMemberDdl/" + id,
        method: "GET",
    });
}

/**
 * 获取未读消息
 */
export function GetMessageTotalAPI(id) {
    return request({
        url: "/pureut-app/team/getTeamMemberDdl",
        method: "GET",
    });
}

/**
 * 扫一扫
 * 扫一扫类型（1确认扫一扫2校验扫一扫）
 * @param {*} params
 * @param {type} id
 * @param {type} scanType
 * @param {type} deviceCode
 * @returns
 * "status":"1"  不通过
 * "status":"0" 通过
 */
export function GetScanCodeAPI(params) {
    return request({
        url: "/pureut-app/alarmLog/scan",
        method: "GET",
        params,
    });
}

/**
 * 给后台设备id
 */
export function PushDataAPI(params) {
    return request({
        url: "/pureut-app/push/edit/" + params,
        method: "PUT",
    });
}


/**组织架构 */
export const GetTreeSelectAPI = (data) => {
    return request({
        url: "/api/system/dept/treeUserSelect",
        method: "GET",

    });
};
export const GetTreeSelectssAPI = (data) => {
    return request({
        url: "/api/system/dept/treeSelect",
        method: "GET",

    });
};

/**
 * 转交提交
 */

export const GetOvertAPI = (data) => {
    return request({
        url: "/api/safety/audit/hand/over",
        method: "put",
        data

    });
};

/**
 * 抄送
 */

export const GetCopyAPI = (data) => {
    return request({
        url: "/api/safety/audit/copy",
        method: "put",
        data

    });
};
/**
 * 自查抄送
 */

export const GetcopyInfoAPI = (data) => {
    return request({
        url: "/api/self/examination/record/copyInfo",
        method: "put",
        data

    });
};
/**
 * 转交人员
 */

export const GetHandOverTreeUserSelectAPI = (data) => {
    return request({
        url: "/api/system/dept/handOverTreeUserSelect",
        method: "get",
        data

    });
};

/**
 * 版本号
 */

export const GetversionAPI = (data) => {
    return request({
        url: "/system/user/version",
        method: "GET",

    });
};

/**
 * 统计分析部门
 */


export const TreeDeptSelectAPI = (data) => {
    return request({
        url: "/api/system/dept/treeDeptSelect",
        method: "GET",

    });
};

/**
 * 任务列表待处理条数
 */

export const NumtAPI = (data) => {
    return request({
        url: "/api/safety/audit/processed/num",
        method: "GET",

    });
};

/**
 * 修改密码
 */
export const updatePwdAPI = (data) => {
    return request({
        url: "/system/user/profile/updatePwd",
        method: "put",
        isEncrypt: true,
        data

    });
};

/**设备信息 */

export const DeviceLedgerAPI = (data) => {
    return request({
        url: "/eap/deviceLedger/right/" + data,
        method: "GET",

    });
};
// 维修配置列表
export function repairWithAPI(query) {
    return request({
        url: '/eap/repairWith/list',
        method: 'GET',
        params: query
    })
}

/**通过物料组获取物料信息 */
export function sectionAPI(query) {
    console.log();
    return request({
        url: '/system/materiel/section',
        method: 'GET',
        params: query
    })
}

/**权限 */

export const getAppMenuAPI = (query) => {
    return request({
        url: "/system/app/getInfoRoleName",
        method: "GET",
        params: query
    });
};

