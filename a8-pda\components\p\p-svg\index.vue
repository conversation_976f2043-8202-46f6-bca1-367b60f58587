<template>
  <view
    class="p-svg"
    :style="{
      width: size + 'rpx',
      height: size + 'rpx',
    }"
  >
    <image :src="src" />
  </view>
</template>

<script>
export default {
  name: "pSvg",
  props: {
    src: {
      type: String,
      default: "@/static/SVG/tabbar/layout-av.svg",
    },
    size: {
      type: String | Number,
      default: 60,
    },
  },
};
</script>

<style lang="scss" scoped>
.p-svg {
  image {
    width: 100%;
    height: 100%;
  }
}
</style>
