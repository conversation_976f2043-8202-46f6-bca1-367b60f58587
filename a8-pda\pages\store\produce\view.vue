<script>
  import {
    GetListAPI,
    GetViewAPI,
    ReceiptorderItemAPI,
    BarcodeParsingAPI,
  } from "@/api/store/produce";
  import { mapGetters } from "vuex";
  import navigationBar from "../../components/navigationBar.vue";
  import { useDict } from "@/utils/dict";

  export default {
    props: ["type", "condition", "pageNum"],
    computed: {
      ...mapGetters(["getInfo"]),
    },
    components: {
      navigationBar,
    },
    data() {
      return {
        device_maintence_status: [],
        form: {},
        column: [
          { label: "物料编码", prop: "materielCode" },
          { label: "物料描述", prop: "materielName" },

          { label: "单位", prop: "unit" },
          { label: "单据数量", prop: "receiptNum", type: "自定义" },
          {
            label: "待入库数量",
            prop: "stayIntoNum",
            type: "自定义",
          },
        ],

        // 分页查询
        query: {
          pageNum: 1,
          pageSize: 5,
        },
        id: "",
        // 加载状态
        status: "loadng",
        //   列表数据
        tableData: [],
        total: 0,
        type: 0,
        // 字典
        dicts: {},
        // 筛选弹窗
        screenShow: false,
        // 提交审核告警弹窗
        show: false,

        // 派工人列表
        workList: [],
      };
    },
    /**
     * 下拉刷新
     */
    async onPullDownRefresh() {
      try {
        this.tableData = [];
        await this.getList();
      } catch (err) {
      } finally {
        uni.stopPullDownRefresh();
      }
    },
    /**
     * 页面上拉触底事件的处理函数
     */
    async onReachBottom() { },
    async onLoad(otip) {
      this.id = otip.id;
      this.query.receiptId = otip.id;
      this.type = otip.type;
    },
    onShow() {
      this.screenComplete();
      this.tableData = [];
    },
    methods: {
      /**
       * 筛选重置
       */
      async screenReset() {
        await this.getList();
      },
      /**
       * 筛选完成
       */
      async screenComplete() {
        this.getList();
      },
      /**
       * 手动下拉
       */
      async scrolltolower() {
        if (this.status == "nomore") return;
        this.status = "loading";
        this.query.pageNum = ++this.query.pageNum;
        await this.getList();
      },

      /**
       * tabs切换
       */
      tabsChane(v) {
        this.indixAcita = v.status;
        this.query.status = v.status;

        this.search();
      },

      /**扫码 */
      async scanCode() {
        let this_ = this;
        uni.scanCode({
          onlyFromCamera: true,
          success: async function (res) {
            // 扫描二维码，判断设备是否与维保工单绑定设备一致，否则提示“扫描失败！请认准设备扫描”回到上一页；
            if (res.scanType != "QR_CODE")
              uni.showToast({
                title: "请正确扫描二维码",
              });

            //如果扫码长度大于12 默认位追溯码，反之位物料编码
            if (res.result.length > 12) {
              let str = res.result;
              const arr = str.split("#");
              let obj = {
                materielCode: arr[0],
                custProCode: arr[1],
                code: str,
              };
              const { data: result } = await BarcodeParsingAPI(obj);
              //根据返回的提示，解析错误就停止往下走，抛出异常
              if (!result.isOk) {
                uni.showToast({
                  title: result.msg,
                });
                return;
              }
              this_.$set(this_.query, "materielCode", result.data.WLMB);
            } else {
              this_.$set(this_.query, "materielCode", res.result);
            }
          },
        });
      },
      /**
       * 搜索
       */
      search(index) {
        this.query.pageNum = 1;
        this.query.pageSize = 5;
        this.status = "loading";
        this.tableData = [];
        this.getList();
      },

      clear(v) {
        this.query.materielCode = "";

        this.search();
      },
      /**
       * 获取维保列表
       */
      async getList() {
        const { data: result } = await GetViewAPI(this.id);
        const res = await ReceiptorderItemAPI(this.query);

        /**
         * 解决页面不更新
         */
        this.$nextTick(() => {
          this.form = result;
          this.tableData = this.tableData.concat(res.rows);
          this.total = res.total;
          console.log(this.tableData.length, this.total);
          if (this.tableData.length >= this.total) {
            this.status = "nomore";
          }
        });
      },
      /**
       * 跳转
       * @param {*} val
       */
      toPath(val) {
        if (val == 1) {
          uni.navigateBack({
            delta: 1,
          });
        } else {
          uni.switchTab({
            url: "/pages/index/index",
          });
        }
      },
    },

    /**
     * 监听从搜索页的搜索条件
     */
    watch: {},
  };
</script>

<template>
  <view class="page">
    <view>
      <u-navbar title="生产入库任务配送详情" @leftClick="toPath(1)" bgColor="#4669ea" placeholder>
      </u-navbar>
    </view>
    <view class="search">
      <u-input placeholder="物料编码或者追溯码" style="margin: 0 10px" border="surround" v-model="query.materielCode" clearable>
        <u-icon name="scan" slot="prefix" class="#000" size="20" @click="scanCode"></u-icon>
      </u-input>
      <u-button style="width: 40px; height: 35px" type="success" text="搜索" @click="search"></u-button>
      <u-button style="width: 40px; height: 35px; margin-left: 5px" type="error" :plain="true" @click="clear" text="清空">
      </u-button>
    </view>
    <view class="receiptNo">入库单号：{{ form.receiptNo }}</view>
    <view class="box">
      <u-list @scrolltolower="scrolltolower">
        <u-list-item v-for="(t, i) in tableData" :key="i">
          <p-card border class="info-box" v-if="t.stayIntoNum != 0">
            <view class="content">
              <view class="list">
                <u-cell v-for="(item, index) in column" :key="index" :title="item.label + ':'" :border="false">
                  <text slot="value" class="ellipsis-text" v-if="item.type != '自定义'">{{ t[item.prop] }}</text>
                  <view slot="value" v-else class="ellipsis-text">
                    <text v-if="item.prop == 'status'">{{
                      dictText(t.status, device_maintence_status)
                      }}</text>
                    <text v-if="item.label == '待入库数量'" style="color: red; font-weight: 800">{{ t[item.prop] }}</text>
                    <text v-if="item.label == '单据数量'" style="font-weight: 800">{{ t[item.prop] }}</text>
                  </view>
                </u-cell>
                <u-cell :title="''" :border="false">
                  <view slot="value">
                    <u-tag text="配送入库" size="mini" @click="
                        $u.navigateTo(
                          '/pages/store/produce/delivery?id=' + t.itemId
                        )
                      "></u-tag>
                  </view>
                </u-cell>
                <view></view>
              </view>
            </view>
          </p-card>
        </u-list-item>
        <u-loadmore :status="status" />
      </u-list>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .ellipsis-text {
    white-space: nowrap;
    // font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .flex-items {
    width: 180px;

    .title {
      margin-left: 10px;
    }
  }

  .list {}

  // /deep/ .u-icon__icon {
  //   color: #fff !important;
  // }

  /deep/ .u-navbar__content__title {
    color: #fff !important;
  }

  .titles {
    min-width: 100px;
    max-width: 170px;
    font-size: 16px;
    color: #333;
    font-weight: bold;
    margin: 0 5px;
  }

  .ellipsis-title {
    font-weight: 600;
  }

  .page {
    height: calc(100vh - 0px);
    background-color: #5375ff;

    display: flex;
    flex-direction: column;
  }

  .box {
    box-sizing: border-box;
    background: #f1f3f7;
    padding-top: 10rpx;
    // min-height: 100%;
    border-radius: 30rpx;
    flex: 1;
    overflow-y: auto;
    padding-bottom: 150rpx;
  }

  .receiptNo {
    background-color: #f1f3f7;
    padding: 15rpx;
    width: 90%;
    margin: 0 auto;
    border-radius: 15rpx;
    position: relative;
    font-weight: 700;
    margin-bottom: 5rpx;
  }

  .receiptNo::before {
    content: "";
    display: block;
    width: 3px;
    height: 68rpx;
    background-color: #4669ea;
    position: absolute;
    top: 0;
    left: 0;
  }

  .indixAcita {
    border: 1px solid #fff;
    padding: 20rpx;
    border-radius: 35rpx;
  }

  .atcie {
    color: #4669ea;
  }

  .search {
    width: 93%;
    margin: 10rpx auto;
    display: flex;
  }

  .search_item {
    height: 60rpx;
    flex: 1;

    color: #979797;
    text-align: center;
    line-height: 60rpx;
    cursor: pointer;
  }

  .activc {
    background-color: #2e8ff4;

    color: #fff;
  }

  .details {
    color: #2e8ff4;
  }

  .top {
    background-color: #5375ff;
  }

  /deep/ .u-navbar__content {
    // background-color: rgba(0, 0, 0, 0) !important;
  }

  /deep/ .u-cell__body {
    padding: 0px 10px !important;
    font-size: 12px;
  }

  /deep/ .u-cell__title {
    flex: 0.8;
  }

  /deep/ .u-cell__title-text {
    font-size: 12px;
  }

  .info-box {
    margin-top: 20px;
    position: relative;
  }
</style>