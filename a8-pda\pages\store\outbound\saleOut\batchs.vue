<template>
	<!-- 批次推荐 -->
	<view>
		<u-navbar title="批次推荐" @leftClick="toPath()" bgColor="#4669ea" placeholder></u-navbar>
		<!-- 表格 -->
		<uni-table :border="true" stripe emptyText="暂无数据">
			<uni-tr>
				<uni-th width="50" align="center">库位</uni-th>
				<uni-th width="50" align="center">数量</uni-th>
				<uni-th width="200" align="center">追溯码</uni-th>
			</uni-tr>
			<uni-tr v-for="(item,index) in batchRecommendData">
				<uni-td align="center">{{item.storageLocation}}</uni-td>
				<uni-td align="center">{{item.quantity}}</uni-td>
				<uni-td align="center">{{item.traceableCode}}</uni-td>
			</uni-tr>
		</uni-table>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				batchRecommendData: []
			}
		},
		onLoad: function(option) {
			const that = this;
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.on("item", function(data) {
				that.batchRecommendData = data;
			});
		},
		methods: {
			//返回
			toPath() {
				uni.navigateBack({
					delta: 1,
				});
			},
		}

	}
</script>

<style>
</style>