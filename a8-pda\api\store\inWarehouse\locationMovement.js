import request from "@/utils/request"
//验证库位是否存在
export const storageCodeExists = (storageCode) => {
    return request({
        url: '/wms/storageLocation/storageCodeExists',
        method: 'GET',
        params: {
            storageCode
        }
    })
}
//验证原材料追溯码是否存在
export const ylTraceableCodeExists = (traceableCode) => {
    return request({
        url: '/wms/inventory/traceableCodeExists',
        method: 'GET',
        params: {
            traceableCode
        }
    })
}

//验证成品追溯码是否存在
export const cpTraceableCodeExists = (traceableCode) => {
    return request({
        url: '/wms/finishedTraceable/traceableCodeExists',
        method: 'GET',
        params: {
            traceableCode
        }
    })
}
//原材料移库数量查询
export const ylOutQuantity = (storageCode, traceableCode) => {
    return request({
        url: '/wms/inventory/queryQuantity',
        method: 'GET',
        params: {
			storageCode,
            traceableCode
        }
    })
}
//成品移库数量查询
export const cpOutQuantity = (storageCode, traceableCode) => {
    return request({
        url: '/wms/finishedTraceable/queryQuantity',
        method: 'GET',
        params: {
			storageCode,
            traceableCode
        }
    })
}
//根据物料编码查询物料描述
export const queryMaterialName = (materialCode) => {
    return request({
        url: '/system/materiel/queryMaterialName',
        method: 'GET',
        params: {
			materialCode,
        }
    })
}
//原材料库位移动
export const ylLocationMove = (tableData) => {
    return request({
        url: '/wms/inventory/locationMove',
        method: 'POST',
        data: tableData,
    })
}

//成品库位移动
export const cpLocationMove = (tableData) => {
    return request({
        url: '/wms/finishedTraceable/locationMove',
        method: 'POST',
        data: tableData,
    })
}