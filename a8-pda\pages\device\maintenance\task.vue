<script>
import { GetListAPI, GetStatusAPI } from "@/api/index/maintenance";
import { mapGetters } from "vuex";
import navigationBar from "../../components/navigationBar.vue";
import { useDict } from "@/utils/dict";
import { GetUseInfoAPI } from "@/api/common.js";

export default {
  props: ["type", "condition", "pageNum"],
  computed: {
    ...mapGetters(["getInfo", "getUserType"]),
  },
  components: {
    navigationBar,
  },
  data() {
    return {
      device_maintence_status: [],
      list: [
        {
          name: "待提交",
          status: 1,
          color: "#e85a58",
        },
        {
          name: "待处理",
          status: 2,
          color: "#e85a58",
        },
        {
          name: "待确认",
          status: 3,
          color: "#e85a58",
        },

        {
          name: "已完成",
          status: 4,
          color: "#9bdb72",
        },
        {
          name: "关结",
          status: 5,
          color: "#9bdb72",
        },
        {
          name: "已超时",
          status: 6,
          color: "#bacef1",
        },
      ],

      column: [
        { label: "设备编号", prop: "deviceNo" },
        { label: "设备名称", prop: "deviceName" },
        { label: "报修人", prop: "repairName" },
        {
          label: "维修分类",
          prop: "repairTypeName",
        },
        { label: "故障类型", prop: "breakdownTypeName" },
        { label: "报修时间", prop: "repairTime" },
        {
          label: "设备类型",
          prop: "deviceType",
        },
        { label: "规格型号", prop: "type" },
        { label: "处理结果", prop: "repairResult", type: "自定义" },

        { label: "是否超时", prop: "isSuper", type: "自定义" },
      ],
      baseList: [
        {
          icon: "../../../static/images/sy.png",
          title: "首页",
          url: "/pages/index/index",
        },
        {
          icon: "../../../static/images/spzx.png",
          title: "审批中心",
          url: "1",
        },
        {
          icon: "../../../static/images/tj.png",
          title: "统计",
          url: "1",
        },
      ],
      // 分页查询
      query: {
        pageNum: 1,
        pageSize: 5,

        //  是否数据隔离
        status: 2,
      },
      // 加载状态
      status: "loadng",
      //   列表数据
      tableData: [],
      total: 0,
      // 字典
      dicts: {},
      // 筛选弹窗
      screenShow: false,
      // 提交审核告警弹窗
      show: false,
      userId: "",

      // 派工人列表
      workList: [],
    };
  },
  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      this.tableData = [];
      this.status = "loading";
      this.query.pageNum = 1;
      await this.getList();
    } catch (err) {
    } finally {
      uni.stopPullDownRefresh();
    }
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  async onReachBottom() {},
  async onLoad() {},
  async onShow() {
    let { data: result } = await GetUseInfoAPI();
    this.query.userId = result.user.userId;
    this.screenComplete();
    this.dict();
  },
  methods: {
    /**
     * 标签页切换时触发 ( 0:全部 1:待派工 2:维保中 3:待审核 4:维保完成 )
     * @param {*} item
     */
    tabsChange(item) {
      this.query.status = item.status;

      this.screenComplete();
    },
    /**获取字典 */
    async dict() {
      this.device_maintence_status = await useDict("device_maintence_status");
    },
    /**
     * 筛选重置
     */
    async screenReset() {
      this.screenShow = false;
      this.query.mineType = 0;
      this.tableData = [];
      this.query.pageNum = 1;
      this.status = "loading";
      await this.getList();
    },
    /**
     * 筛选完成
     */
    async screenComplete() {
      this.query.pageNum = 1;
      this.status = "loading";
      this.tableData = [];
      // const { data: res } = await GetStatusAPI();
      // this.list = res;
      this.getList();
    },
    /**
     * 手动下拉
     */
    async scrolltolower() {
      if (this.status == "nomore") return;
      this.status = "loading";
      this.query.pageNum = ++this.query.pageNum;
      await this.getList();
    },

    /**
     * tabs切换
     */
    tabsChane(v) {
      this.indixAcita = v.status;
      this.query.status = v.status;

      this.search();
    },
    /**
     * 搜索
     */
    search(index) {
      console.log(this.query.time);
      this.show = false;
      this.query.pageNum = 1;
      this.query.pageSize = 5;
      if (index || index == 0) {
        this.query.status = index + 1;
      }
      this.status = "loading";

      this.tableData = [];
      this.getList();
    },

    confirm(v) {
      let date = v.range.data;
      this.query.beginTime = `${date[0]} 00:00:00`;
      this.query.endTime = `${date[date.length - 1]} 23:59:59`;

      console.log(this.query, " this.query");
      this.search();
    },
    /**
     * 获取维保列表
     */
    async getList() {
      const { data: result } = await GetListAPI(this.query);
      /**
       * 解决页面不更新
       */
      this.$nextTick(() => {
        this.tableData = this.tableData.concat(result.rows);
        this.total = result.total;
        console.log(this.tableData, "this.tableData");
        if (this.tableData.length >= this.total) {
          this.status = "nomore";
        }
      });
    },
    /**
     * 跳转
     * @param {*} val
     */
    toPath(val) {
      console.log("v", val);
      if (val == 1) {
        uni.navigateBack({
          delta: 1,
        });
      } else {
        uni.switchTab({
          url: "/pages/index/index",
        });
      }
    },
    open() {
      this.$refs.calendar.open();
    },

    /**跳转详情 */
    toView(t) {
      try {
        //当前是申请人时根据状态跳转不同的页面,是去详情还是去操作页面
        console.log(this.getInfo, this.getUserType, "this.getInfo.userType");
        if (this.getUserType == 1) {
          let url =
            t.status == 1
              ? "/pages/device/maintenance/fillIn?id="
              : t.status == 3
              ? "/pages/device/maintenance/confirm?id="
              : "/pages/device/maintenance/view?id=";

          uni.navigateTo({
            url: url + t.fillId,
          });
        }
        //当前是维修人时根据状态跳转不同的页面,是去详情还是去操作页面
        if (this.getUserType == 2) {
          let url =
            t.status == 2
              ? "/pages/device/maintenance/handle?id="
              : "/pages/device/maintenance/view?id=";
          uni.navigateTo({
            url: url + t.fillId,
          });
        }
        //监管人员只能查看
        if (this.getUserType == 3) {
          uni.navigateTo({
            url: "/pages/device/maintenance/view?id=" + t.fillId,
          });
        }
      } catch (err) {}
    },
  },

  /**
   * 监听从搜索页的搜索条件
   */
  watch: {},
};
</script>

<template>
  <view class="page">
    <view>
      <u-navbar
        title="设备维修任务"
        @leftClick="toPath(1)"
        bgColor="#ffffff"
        placeholder
      >
        <view slot="right">
          <view @click="open()">时间范围</view>
        </view>
      </u-navbar>

      <view class="search">
        <view
          class="search_item"
          v-for="(item, index) in list"
          :key="index"
          :class="{ activc: query.status == index + 1 }"
          @click="search(index)"
          >{{ item.name }}</view
        >
      </view>
    </view>

    <view class="box">
      <u-list @scrolltolower="scrolltolower" v-if="tableData.length != 0">
        <u-list-item v-for="(t, i) in tableData" :key="i">
          <p-card border class="info-box">
            <template #header>
              <view class="flex-between">
                <view class="flex-items">
                  <view class="ellipsis"> 设备维修单号：{{ t.fillCode }}</view>
                </view>
                <view class="details" @click="toView(t)">详情</view>
              </view>
            </template>

            <view class="content">
              <view class="list">
                <u-cell
                  v-for="(item, index) in column"
                  :key="index"
                  class="list_item"
                  :title="item.label + ':'"
                  :border="false"
                >
                  <text
                    slot="value"
                    class="ellipsis-text"
                    v-if="item.type != '自定义'"
                    >{{ t[item.prop] }}</text
                  >
                  <view slot="value" v-else class="ellipsis-text">
                    <text v-if="item.prop == 'repairResult'">{{
                      dictText(t.repairResult, [
                        { text: "完成", value: "1" },
                        { text: "待回货", value: "2" },
                        { text: "委外处理", value: "3" },
                        { text: "已关结", value: "4" },
                      ])
                    }}</text>
                    <text
                      v-if="item.prop == 'isSuper'"
                      :style="`color:${t.isSuper == 1 ? '#a3f5df' : '#e42c40'}`"
                      >{{
                        dictText(t.isSuper, [
                          { value: "1", text: "已超时" },
                          { value: "2", text: "未超时" },
                        ])
                      }}</text
                    >
                  </view>
                </u-cell>

                <view></view>
              </view>
            </view>
          </p-card>
        </u-list-item>
        <u-loadmore :status="status" />
      </u-list>
      <u-empty mode="data" class="empty" v-if="tableData.length == 0">
      </u-empty>
    </view>
    <uni-calendar
      ref="calendar"
      class="uni-calendar--hook"
      :clear-date="true"
      :insert="false"
      :lunar="false"
      :range="true"
      @confirm="confirm"
      @close="close"
    />
  </view>
</template>

<style lang="scss" scoped>
.ellipsis-text {
  width: 50%;
  white-space: nowrap;
  // font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.empty {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.flex-items {
  flex: 1;

  .title {
    margin-left: 10px;
  }
}

.list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.list_item {
  width: 50%;
  font-size: 12px;
}

.titles {
  min-width: 100px;
  max-width: 170px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin: 0 5px;
}

.page {
  height: calc(100vh - 0px);
  background-color: #5375ff;

  display: flex;
  flex-direction: column;
}

.box {
  box-sizing: border-box;
  background: #f1f3f7;
  padding-top: 10rpx;
  // min-height: 100%;
  border-radius: 30rpx;
  flex: 1;
  overflow-y: auto;
  // padding-bottom: 150rpx;
}

.indixAcita {
  border: 1px solid #fff;
  border-radius: 35rpx;
}

.search {
  width: 80%;
  margin: 10rpx auto;
  display: flex;
}

.search_item {
  height: 60rpx;
  flex: 1;

  color: #979797;
  text-align: center;
  line-height: 60rpx;
  cursor: pointer;
}

.activc {
  background-color: #2e8ff4;

  color: #fff;
}

.details {
  color: #2e8ff4;
}

// .search_item:nth-of-type(2) {
//   border-left: 1px solid #979797;
//   border-right: 1px solid #979797;
// }
.bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  height: 150rpx;
  width: 100%;
  border-top: 1px solid #747474;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tabs {
  height: 100rpx;
  display: flex;
  justify-content: space-evenly;
  background-color: #5375ff;
  align-items: center;

  .tabs-list {
    color: #fff;
    padding: 1rpx 0rpx;
    width: 17%;
    display: flex;
    font-size: 25rpx;
    align-items: center;
    height: 50%;
    justify-content: center;
    position: relative;

    .badge {
      top: 0;
      position: absolute;
      right: -10%;
    }
  }
}

.top {
  background-color: #5375ff;
}

/deep/ .u-navbar__content {
  // background-color: rgba(0, 0, 0, 0) !important;
}

/deep/ .u-cell__body {
  padding: 0px 10px !important;
  font-size: 12px;
}

/deep/ .u-cell__title {
  flex: 0.8;
}

/deep/ .u-cell__title-text {
  font-size: 12px;
}

.info-box {
  margin-top: 20px;
}

.content {
  width: 100%;
  margin-top: 20px;

  .content-item {
    display: flex;
    align-items: center;
    margin-left: 10px;
    width: 100%;

    .label {
      color: #747474;
    }
  }
}

.bottom-items {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  align-items: center;
}

.tabsBox {
  background-color: #ffffff;
  border: 1px solid #000;
  border-left: 0 solid #000;
  border-right: 0 solid #000;
  position: relative;

  .screen-box {
    position: absolute;
    width: 100vw;
    top: 40px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;

    .screen-content {
      height: 100px;
      width: 100vw;
      padding: 20px;
      background-color: #ffffff;
      border-bottom: 1px solid #000;

      .screen-my {
        width: 80px;
        height: 10px;
        color: #fff;
        padding: 10px;
        cursor: pointer;
        line-height: 10px;
        text-align: center;
        border-radius: 5px;
        background: #d1d1d1;
        border: 1px solid #d1d1d1;
      }

      .screen-my-av {
        background: #007aff;
        border: 1px solid #007aff;
      }
    }

    .screen-bottom {
      display: flex;
      justify-content: space-between;
    }
  }
}

.workBy {
  border: 1px solid #ebebeb;
  min-height: 30px;
  min-width: 200px;
  padding: 5px;
  margin-top: 10px;
  border-radius: 5px;
  flex-wrap: wrap;

  .workByItem {
    padding: 0 5px;
    margin: 0 5px;
    margin-bottom: 5px;
    border-radius: 5px;
    background-color: #e9f5fd;
    color: #239ceb;
  }
}
</style>