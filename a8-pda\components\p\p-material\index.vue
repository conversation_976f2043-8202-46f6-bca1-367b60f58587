<script>
import { repairWithAPI, sectionAPI } from "@/api/common";

export default {
  props: ["list", "column", "title"],

  data() {
    return {
      teamsList: [], //班组下拉
      teamsFrom: {
        teamId: "",
        teamUser: "",
        teamUserIds: [],
      }, //班组信息
      range: [
        { value: "materielName", text: "物料描述" },
        { value: "materielGroup", text: "物料组" },
        { value: "materielCode", text: "物料编码" },
      ],
      teams: [],
      query: { name: "" }, //搜索条件
      checkboxValue1: [],
      materialList: [],
      dataObj: {},
      value: "materielName",
      checkboxList: [],
      parms: { materielGroupId: "" },
    };
  },

  methods: {
    /**获取详情 */
    async getPassword() {
      try {
        let res = await repairWithAPI();

        this.parms.materielGroupId = res.rows[0].materielGroupId;

        const result = await sectionAPI(this.parms);
        this.materialList = result.data;
      } catch {}
    },
    changeSelect() {
      this.range.map((t) => {
        console.log(t);
        this.$set(this.parms, t.value);
      });
      this.query.name = "";
    },
    /**确定 */
    submit() {
      console.log(this.checkboxValue1, "checkboxValue1");
      let arr = [];

      this.materialList.map((t) => {
        if (this.checkboxValue1.includes(t.materielId)) {
          arr.push(t);
        }
      });
      /**给父组件传参 */
      this.$emit("change", arr);
    },
    /**搜索 */
    async search() {
      this.parms[this.value] = this.query.name;
      console.log(this.parms, "this.parms");
      const result = await sectionAPI(this.parms);
      this.materialList = result.data;
    },
  },

  /**
   * 获取班组下拉
   */
  async mounted() {
    this.getPassword();
  },
  /**
   * 监听从搜索页的搜索条件
   */
  watch: {
    column: {
      handler(n, o) {
        this.columnList = this.column;
        // console.log(this.columnList, "this.columnList");
      },
      immediate: true,
    },
  },
};
</script>
<template>
  <view class="p-material">
    <view class="title">{{ title }}</view>
    <view class="search">
      <uni-data-select
        v-model="value"
        :localdata="range"
        @change="changeSelect()"
      ></uni-data-select>
      <u--input
        placeholder="支持通过物料描述"
        border="surround"
        shape="circle"
        v-model="query.name"
        clearable
      >
      </u--input>
      <u-button
        style="flex: 0.1; height: 30px"
        type="primary"
        size="mini"
        @click="search"
        >查询</u-button
      >
    </view>

    <view class="contetn">
      <u-checkbox-group
        v-model="checkboxValue1"
        placement="column"
        @change="checkboxChange"
      >
        <view class="contetn_item" v-for="(t, i) in materialList" :key="i">
          <view class="contetn_item_left">
            <view>
              {{ t.materielName }}
            </view>
            <view class="contetn_item_left_text">
              物料组：{{ t.groupName }} 物料编码:{{ t.materielCode }} 单位:{{
                t.unitName
              }}
            </view>
          </view>
          <view class="contetn_item_right">
            <u-checkbox
              shape="circle"
              :customStyle="{ marginBottom: '8px' }"
              :name="t.materielId"
            >
            </u-checkbox>
          </view>
        </view>
      </u-checkbox-group>
    </view>
    <u-button
      class="but"
      type="primary"
      text="确定"
      @click="submit()"
    ></u-button>
  </view>
</template>

<style lang="scss" scoped>
.p-material {
  height: 80vh;
  padding: 30rpx 30rpx;
  position: relative;
  box-sizing: border-box;
}
.but {
  position: absolute;
  bottom: 5%;
  left: 50%;
  width: 80%;
  transform: translateX(-50%);
}
.search {
  display: flex;

  align-items: center;
}
.title {
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
}
.contetn {
  height: 70%;
  overflow: auto;
}
.contetn_item {
  height: 100rpx;
  border-top: 1px solid #c0bfbf;
  display: flex;
  padding: 10rpx;
  margin-top: 20rpx;
  box-sizing: border-box;
  .contetn_item_left {
    width: 90%;
    height: 100%;
    font-size: 14px;
    color: #5e5c5c;
    // background-color: aqua;
  }
  .contetn_item_left_text {
    font-size: 12px;
    margin-top: 10rpx;
    color: #aaaaaa;
  }
  .contetn_item_right {
    width: 10%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>