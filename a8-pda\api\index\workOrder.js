import request from "@/utils/request"
/**
 * 填报任务
 * @returns {object} params 查询条件
 */
export const GetListAPI = (params) => {
  return request({
    url: "/api/safety/audit/list",
    method: "GET",
    params,
  })
}

/**
 *表头
 * @returns 
 */
export const GetStatusAPI = (params) => {
  return request({
    url: "/api/safety/audit/status/list",
    method: "GET",
  })
}
/**
 * --------------------------维保页面--STA------------------------------->
 * 获取详情
 * @returns {object} data
 */
export const GetViewAPI = (data) => {
  return request({
    url: "/api/safety/audit/" + data,
    method: "GET",
  })
}

/**
 * 提交审核
 * @returns {object} data
 */
export const SubExamineAPI = (data) => {
  return request({
    url: "/app/maintenanceWorkOrder/saveSubmit",
    method: "PUT",
    data,
  })
}

/**
 * 新增信息
 * @returns {object} data
 */
export const AddDataAPI = (data) => {
  return request({
    url: "/equipment/maintenance-work-order/audit",
    method: "PUT",
    data,
  })
}


/**
 * 安全员回复
 */
export const AddSafeAPI = (data) => {
  return request({
    url: "/api/safety/audit/safe/reply",
    method: "POST",
    data,
  })
}


/**
 * 安环回复
 */
export const AddSecurityAPI = (data) => {
  return request({
    url: "/api/safety/audit/security/reply",
    method: "POST",
    data,
  })
}

/**
 * 暂停
 */

export const PauseAPI = (data) => {
  return request({
    url: "/api/safety/audit/pause",
    method: "put",
    data,
  })
}


/**
 * 流转信息
 */

export const GetTurnoverAPI = (data) => {
  return request({
    url: "/api/safety/audit/turnover/list",
    method: "GET",
    params: {
      id: data
    }
  })
}