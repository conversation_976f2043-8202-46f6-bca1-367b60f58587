<template>
	<view>
		<view>
			<u-navbar :title="navbarTitle" @leftClick="toPath()" bgColor="#4669ea" placeholder></u-navbar>
			<uni-section v-if="currentTabIndex === 0" :title="'调拨单号:' + item.transferOrderNo" type="line"
				style="border-radius: 10rpx; margin-left: 10rpx; margin-right: 10rpx; margin-top: 10rpx;">
				<template v-slot:right>
					<uni-icons :type="iconsType" @click="iconsClick"></uni-icons>
				</template>
			</uni-section>
		</view>
		<uni-card v-if="isCardVisible && currentTabIndex === 0">
			<view style="display: flex; justify-content: space-between">
				<text>物料编码: </text>
				<text>{{ item.materielCode }}</text>
			</view>
			<view style="display: flex; justify-content: space-between">
				<text>物料描述: </text>
				<text>{{ item.materielName }}</text>
			</view>
			<view style="display: flex; justify-content: space-between">
				<text>调出仓库: </text>
				<text>{{ item.location }}</text>
			</view>
			<view style="display: flex; justify-content: space-between">
				<text>调入仓库: </text>
				<text>{{ item.receiveLocation }}</text>
			</view>
			<view style="display: flex; justify-content: space-between">
				<text>单位: </text>
				<text>{{ item.unit }}</text>
			</view>
			<view style="display: flex; justify-content: space-between">
				<text>单据数量: </text>
				<text>{{ item.qty }}</text>
			</view>
			<view style="display: flex; justify-content: space-between">
				<text>待入库数量: </text>
				<text>{{ pendingInboundQuantity  }}</text>
			</view>
		</uni-card>
		<view class="tabs-bg">
			<uv-tabs :list="tabList" @click="changeTabs" :current="currentTabIndex"
				:itemStyle="{flex: 1, height: '44px'}"></uv-tabs>
		</view>
		<view v-if="currentTabIndex===0" class="form">
			<uni-forms ref="baseFormRef" v-model="formData" :border="true" :label-width="100" :label-align="'left'">
				<uni-data-checkbox v-model="formData.codeType" :localdata="codeTypeOption"></uni-data-checkbox>

				<uni-forms-item label="调出库位" :border="true" name="storageCode">
					<uni-easyinput v-model="formData.storageCode" placeholder="请扫描目标库位码" :inputBorder="false"
						:placeholderStyle="`fontSize:12px;color:#999`" @change="storageCodeInputChange"
						@iconClick="storageCodeIconClick" prefixIcon="scan"></uni-easyinput>
				</uni-forms-item>
				<uni-forms-item label="追溯码" :border="true" name="traceableCode">
					<uni-easyinput v-model="formData.traceableCode" placeholder="请扫描入库产品追溯码" :inputBorder="false"
						:placeholderStyle="`fontSize:12px;color:#999`" @change="traceableCodeInputChange"
						@iconClick="traceableCodeIconClick" prefixIcon="scan"></uni-easyinput>
				</uni-forms-item>
				<uni-forms-item label="出库数量" :border="true" name="inboundQuantity">
					<uni-easyinput type="number" :inputBorder="false" v-model="formData.inboundQuantity"
						:placeholderStyle="`fontSize:12px;color:#999`" @change="inboundQuantityInputChange"
						:disabled="!isInboundQuantityEnabled"></uni-easyinput>
				</uni-forms-item>
			</uni-forms>

			<view>
				<button class="btn-submit" type="primary" @click="confirm">入库确认</button>
			</view>

			<!-- 表格 -->
			<view style="margin-top: 10rpx ;">
				<uni-table :border="true" stripe emptyText="暂无数据" style="border-radius: 20rpx;">
					<uni-tr>
						<uni-th width="200" align="center">追溯码</uni-th>
						<uni-th width="100" align="center">出库数量</uni-th>
					</uni-tr>
					<uni-tr v-for="(item, index) in tableData" :key="index">
						<uni-td align="center" style="word-break: break-all;">{{item.traceableCode}}</uni-td>
						<uni-td align="center">{{item.inboundQuantity}}</uni-td>
					</uni-tr>
				</uni-table>
			</view>
		</view>

		<view v-if="currentTabIndex===1" class="form">
			<view style="margin-top: 10rpx;">
				<uni-data-select placeholder="请选择物料" v-model="queryData.materielName" :localdata="materielNameOption"
					@change="materielNameChange" @></uni-data-select>
			</view>
			<!-- 表格 -->
			<view style="margin-top: 15rpx ;">
				<uni-table :border="true" stripe emptyText="暂无数据" style="border-radius: 20rpx;">
					<uni-tr>
						<uni-th width="200" align="center">追溯码</uni-th>
						<uni-th width="100" align="center">入库数量</uni-th>
					</uni-tr>
					<uni-tr v-for="(item, index) in tableData1" :key="index">
						<uni-td align="center" style="word-break: break-all;">{{item.traceableCode}}</uni-td>
						<uni-td align="center">{{item.scannedQuantity}}</uni-td>
					</uni-tr>
				</uni-table>
			</view>
		</view>
		<u-toast ref="toastRef"></u-toast>
	</view>
	</view>
</template>

<script>
	import {
		SaveInboundDataAPI,
		queryAllScan,
		queryMaterielScan
	} from "@/api/store/inWarehouse/inWarehouse"
	export default {
		data() {
			return {
				item: {},
				iconsType: "down",
				isCardVisible: true,
				tabList: [{
					name: "扫码入库"
				}, {
					name: "历史扫码"
				}],
				currentTabIndex: 0,
				formData: {
					codeType: 0,
				},
				codeTypeOption: [{
					text: '批次码',
					value: 0
				}, {
					text: '序列号',
					value: 1,
					disable: true
				}, {
					text: '集成码',
					value: 2,
					disable: true
				}],
				tableData: [],
				tableData1: [],
				queryData: {},
				materielNameOption: []
			};
		},
		onLoad: function(option) {
			const that = this;
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.on("item", function(data) {
				that.item = data;
			});

		},
		computed: {
			isInboundQuantityEnabled() {
				return !!this.formData.traceableCode && !!this.formData.storageCode;
			},
			pendingInboundQuantity() {
				return Number(this.item.qty) - Number(this.item.scannedQuantity);
			},
			navbarTitle() {
				return this.currentTabIndex === 1 ? '调拨入库拣货历史' : '调拨入库拣货';
			}
		},
		methods: {
			changeTabs(item) {
				this.currentTabIndex = item.index
				if (item.index === 1) {
					this.queryAllScan()
				}
			},
			//返回
			toPath() {
				uni.navigateBack({
					delta: 1,
				});
			},
			iconsClick() {
				this.isCardVisible = !this.isCardVisible;
				this.iconsType = this.isCardVisible ? "down" : "up";
			},
			//库位
			storageCodeInputChange(val) {
				if (!val) return;
				this.formData.storageCode = val
			},
			storageCodeIconClick() {
				const that = this
				uni.scanCode({
					onlyFromCamera: true,
					success: function(val) {
						that.$set(that.formData, "storageCode", val.result)
					},
					fail() {
						that.$refs.toastRef.show({
							type: "error",
							message: "识别失败，请重新识别。"
						})
						that.$set(that.formData, "storageCode", '')
					}
				})
			},
			//追溯码
			traceableCodeInputChange(val) {
				if (!val) return;
				// 校验追溯码
				const codePrefix = val.split('#')[0];
				if (codePrefix !== this.item.materielCode) {
					this.$refs.toastRef.show({
						type: "error",
						message: "追溯码与物料编码不匹配！"
					});
					this.formData.traceableCode = '';
					return;
				}
				this.formData.traceableCode = val;
			},
			traceableCodeIconClick() {
				const that = this;
				uni.scanCode({
					onlyFromCamera: true,
					success: function(val) {
						const code = val.result;
						//物料编码校验
						const codePrefix = code.split('#')[0];
						if (codePrefix !== that.item.materielCode) {
							that.$refs.toastRef.show({
								type: "error",
								message: "追溯码与物料编码不匹配！"
							});
							that.$set(that.formData, "traceableCode", '');
							return;
						}
						that.$set(that.formData, "traceableCode", code);
					},
					fail() {
						that.$refs.toastRef.show({
							type: "error",
							message: "识别失败，请重新识别。"
						});
						that.$set(that.formData, "traceableCode", '');
					}
				});
			},
			//入库数量
			inboundQuantityInputChange(val) {
				if (!this.isInboundQuantityEnabled) {
					this.formData.inboundQuantity = '';
					return;
				}
				if (!val) return;
				// 验证是否为整数
				if (!/^[1-9]\d*$/.test(val)) {
					this.$refs.toastRef.show({
						type: "warning",
						message: "请输入正整数。"
					});
					this.formData.inboundQuantity = '';
					return;
				}
				if (Number(val) > this.pendingInboundQuantity) {
					this.$refs.toastRef.show({
						type: "warning",
						message: "入库数量不能大于待入库数量！"
					});
					this.formData.inboundQuantity = '';
					return;
				}
				this.formData.inboundQuantity = val
			},
			//入库确认
			confirm() {
				if (!/^[1-9]\d*$/.test(this.formData.inboundQuantity)) {
					this.$refs.toastRef.show({
						type: "warning",
						message: "请输入正整数。"
					});
					this.formData.inboundQuantity = '';
					return;
				}
				if (Number(this.formData.inboundQuantity) > this.pendingInboundQuantity) {
					this.$refs.toastRef.show({
						type: "warning",
						message: "出库数量不能大于待出库数量！"
					});
					this.formData.inboundQuantity = '';
					return;
				}
				let params = this.item
				params.inboundQuantity = this.formData.inboundQuantity
				params.traceCode = this.formData.traceableCode
				params.warehouseLocation = this.formData.storageCode
				const row = {
					codeType: this.formData.codeType,
					traceableCode: this.formData.traceableCode,
					inboundQuantity: Number(this.formData.inboundQuantity),
				};
				SaveInboundDataAPI(params).then(res => {
					this.headerId = res.data
					this.$set(this.item, "scannedQuantity", Number(this.item.scannedQuantity) + Number(row
						.inboundQuantity))
					// 将数据存入tableData
					const existingIndex = this.tableData.findIndex(item =>
						item.traceableCode === row.traceableCode
					);
					if (existingIndex !== -1) {
						// 存在则累加数量
						this.tableData[existingIndex].inboundQuantity += row.inboundQuantity;
					} else {
						// 不存在则添加新条目
						this.tableData.push(row);
					}
					this.$refs.toastRef.show({
						type: "success",
						message: "出库成功。"
					});
				})
				this.formData.storageCode = ''
				this.formData.traceableCode = ''
				this.formData.inboundQuantity = ''
			},
			//查询整单历史扫码
			queryAllScan() {
				const transferOrderNo = this.item.transferOrderNo
				queryAllScan(transferOrderNo).then(res => {
					this.tableData1 = res.data
					const arr = res.data.map(item => ({
						text: item.materielCode,
						value: item.materielCode
					}));

					// 根据 value 去重
					const uniqueArr = [];
					const seen = new Set();
					for (const obj of arr) {
						if (!seen.has(obj.value)) {
							seen.add(obj.value);
							uniqueArr.push(obj);
						}
					}
					this.materielNameOption = uniqueArr;
				})
			},
			materielNameChange(val) {
				if (val) {
					const transferOrderNo = this.item.transferOrderNo
					const materielCode = val
					queryMaterielScan(transferOrderNo, materielCode).then(res => {
						this.tableData1 = res.data
					})
				} else {
					const transferOrderNo = this.item.transferOrderNo
					queryAllScan(transferOrderNo).then(res => {
						this.tableData1 = res.data
					})
				}

			}
		}
	};
</script>

<style lang="scss" scoped>
	.lists {
		display: flex;
		justify-content: space-around;
		margin: 20rpx 0;
	}

	.tabs-bg {
		margin: 10rpx;
		border-radius: 10rpx;
		background: #fff;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		/* 让背景宽度100% */
		width: auto;
	}

	.form {
		flex: 1;
		margin: 10rpx;
		overflow: auto;
		padding: 5rpx 35rpx;
		border-radius: 10rpx;
		background-color: #ffffff;
	}
</style>