\<template>
  <view class="content">
    <view class="text-area">
      <button @click="startLuyin" v-show="luyinStatus" class="recordingStyle">
        开始录音
      </button>
      <button @click="endLuyin" v-show="!luyinStatus" class="recordingStyle">
        结束录音
      </button>
    </view>
    <view class="Sound_btn">
      <button @click="playVoice">试听录音</button>
      <button @click="startYuyin">转文字</button>
    </view>
    <view class="reslut_box" v-show="resContent"></view>
    <p>{{ resContent.result }}</p>
  </view>
  </view>
</template>

<script>
//录音
const recorderManager = uni.getRecorderManager();
//播放录音
const innerAudioContext = uni.createInnerAudioContext();
innerAudioContext.autoplay = true;
export default {
  components: {},
  data() {
    return {
      title: "Hello",
      token: "",
      adioFileData: "",
      adioSize: "",
      resContent: "",
      luyinStatus: true,
    };
  },
  onLoad() {
    let self = this;
    recorderManager.onStop(async function (res) {
      //录音后的回调函数
      console.log("recorder stop" + JSON.stringify(res));
      self.voicePath = res.tempFilePath;
      console.log(res.tempFilePath, "res.tempFilePath");
      // 1. 下载文件到本地（部分平台需要）
      try {
        uni.uploadFile({
          url: "http://*************:8080/resource/oss/upload", //仅为示例，非真实的接口地址
          filePath: res.tempFilePath,
          fileType: "audio",
          name: "file",
          header: {
            // 常见的 token 传递方式，可根据服务器要求调整
            Authorization: "Bearer " + uni.getStorageSync("token"),
          },
          formData: {
            user: "test",
          },
          success: (uploadFileRes) => {
            console.log(uploadFileRes.data, "上传成个");
          },
          fail: (err) => {
            console.log("上传失败");
          },
        });
      } catch (err) {
        console.log(err);
      }

      self.Audio2dataURL(res.tempFilePath);
    });
  },
  methods: {
    startLuyin() {
      console.log("开始录音");
      recorderManager.start({
        duration: 60000, // 录音时长，单位 ms
        sampleRate: 44100, // 采样率，可根据需求调整
        numberOfChannels: 2, // 单声道
        encodeBitRate: 256000, // 编码比特率，对于 PCM 可适当设置
        format: "pcm",
        frameSize: 512,
      });
      this.luyinStatus = false;
    },
    endLuyin() {
      console.log("录音结束");
      recorderManager.stop();
      this.luyinStatus = true;
    },
    async startYuyin() {
      var _this = this;
      //获取token
      const API_KEY = "cfXhdQ46FAUHgMGdorQlvm2i";
      const SECRET_KEY = "t2FEET374t7d4Ux7QpfE7LR0677H3FSB";
      const url = `https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${API_KEY}&client_secret=${SECRET_KEY}`;
      const res = await uni.request({ url });
      console.log(res);
      _this.token = res[1].data.access_token;

      _this.PostData();
    },

    PostData() {
      let url = `https://vop.baidu.com/server_api`;
      //调用语音识别接口
      uni.request({
        url: url, //仅为示例，并非真实接口地址。
        data: JSON.stringify({
          format: "pcm",
          rate: 16000,
          channel: 1,
          cuid: "Rqpb50jIKEy0Inwq6ulFpdhcVVKcDtF0",
          token: "",
          dev_pid: 1537,
          speech: this.adioFileData,
          len: this.adioSize,
          token: this.token,
        }),
        header: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        method: "POST",
        success: (res) => {
          this.resContent = res.data;
          console.log(JSON.stringify(res.data) + "识别结果");
        },
      });
    },

    Audio2dataURL(path) {
      var _this = this;
      plus.io.resolveLocalFileSystemURL(path, function (entry) {
        entry.file(
          function (file) {
            var reader = new plus.io.FileReader();
            _this.adioSize = file.size;
            reader.onloadend = function (e) {
              //   console.log(e.target.result);
              _this.adioFileData = e.target.result.split(",")[1];
            };
            reader.readAsDataURL(file);
            _this.startYuyin();
          },
          function (e) {
            alert(e.message);
            // mui.toast("读写出现异常: " + e.message);
          }
        );
      });
    },
    //播放

    playVoice() {
      console.log("播放录音");
      if (this.voicePath) {
        innerAudioContext.src = this.voicePath;
        innerAudioContext.play();
      }
    },
  },
};
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 300px;
}

.logo {
  height: 200 rpx;
  width: 200 rpx;
  margin-top: 200 rpx;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50 rpx;
}

.text-area {
  display: flex;
  justify-content: center;
  margin-top: 10 vh;
}

.text-area.recordingStyle {
  width: 100 px;
  height: 100 px;
  border-radius: 50%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16 px;
  color: #fff;
  background: #409eff;
}

.Sound_btn {
  display: flex;
  align-items: center;
  margin: 10px 0 0 0;
}

.Sound_btn > button {
  background: none;
}

.title {
  font-size: 36rpx;
  color: #8f8f94;
}

.reslut_box {
  width: 90%;
  background: #409eff10;
  margin: 1em 5%;
  padding: 0.5em 0;
  border-radius: 10px;
}

.reslut_box p {
  margin: 0 20px;
}
</style>