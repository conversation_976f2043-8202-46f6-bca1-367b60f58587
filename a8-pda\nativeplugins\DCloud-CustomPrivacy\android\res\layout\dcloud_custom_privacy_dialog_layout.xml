<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_content_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/dcloud_dialog_shape"
    android:gravity="center"
    android:orientation="vertical">


    <TextView
        android:id="@+id/tv_custom_privacy_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:gravity="center"
        android:paddingTop="20dp"
        android:textColor="#000000"
        android:textSize="16sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_privacy_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="500dp"
        android:paddingLeft="12dp"
        android:paddingTop="6dp"
        android:textColor="#000000"
        android:paddingRight="12dp"
        android:paddingBottom="2dp"
        android:scrollbars="vertical"
        android:tag="{'linkColor':'#0000FF','linkLine':false}" />


<!--    <LinearLayout-->
<!--        android:id="@+id/ll_buttons_default"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:paddingLeft="12dp"-->
<!--        android:paddingTop="2dp"-->
<!--        android:paddingRight="12dp"-->
<!--        android:paddingBottom="8dp">-->


<!--        <Button-->
<!--            android:id="@+id/btn_custom_privacy_cancel"-->
<!--            style="?android:attr/borderlessButtonStyle"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_margin="2dp"-->
<!--            android:layout_weight="1"-->
<!--            android:background="@drawable/dcloud_custom_rich_dialog_button_bg_selecter"-->
<!--            android:paddingLeft="12dp"-->
<!--            android:paddingTop="10dp"-->
<!--            android:paddingRight="12dp"-->
<!--            android:paddingBottom="10dp"-->
<!--            android:text="@string/dcloud_common_cancel"-->
<!--            android:textColor="@drawable/dcloud_custom_rich_dialog_button_text_selecter"-->
<!--            android:textSize="14sp"-->
<!--            android:visibility="gone" />-->

<!--        <Button-->
<!--            android:id="@+id/btn_custom_privacy_sure"-->
<!--            style="?android:attr/borderlessButtonStyle"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_margin="2dp"-->
<!--            android:layout_weight="1"-->
<!--            android:background="@drawable/dcloud_custom_rich_dialog_button_bg_selecter"-->
<!--            android:paddingLeft="12dp"-->
<!--            android:paddingTop="10dp"-->
<!--            android:paddingRight="12dp"-->
<!--            android:paddingBottom="10dp"-->
<!--            android:text="@string/dcloud_common_ok"-->
<!--            android:textColor="@drawable/dcloud_custom_rich_dialog_button_text_selecter"-->
<!--            android:textSize="14sp"-->
<!--            android:visibility="gone" />-->

<!--    </LinearLayout>-->


    <LinearLayout
        android:id="@+id/ll_buttons_with_visitor"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingLeft="12dp"
        android:paddingTop="2dp"
        android:paddingRight="12dp"
        android:paddingBottom="8dp">




        <Button
            android:id="@+id/btn_custom_privacy_sure"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@drawable/dcloud_custom_rich_dialog_button_bg_selecter"
            android:paddingLeft="12dp"
            android:paddingTop="10dp"
            android:paddingRight="12dp"
            android:paddingBottom="10dp"
            android:text="@string/dcloud_common_ok"
            android:textColor="@drawable/dcloud_custom_rich_dialog_button_text_selecter"
            android:textSize="14sp"
            android:visibility="gone" />


        <Button
            android:id="@+id/btn_custom_privacy_cancel"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@drawable/dcloud_custom_rich_dialog_button_bg_selecter"
            android:paddingLeft="12dp"
            android:paddingTop="10dp"
            android:paddingRight="12dp"
            android:paddingBottom="10dp"
            android:text="@string/dcloud_common_cancel"
            android:textColor="@drawable/dcloud_custom_rich_dialog_button_text_selecter"
            android:textSize="14sp"
            android:visibility="gone" />


        <Button
            android:id="@+id/btn_custom_privacy_visitor"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@drawable/dcloud_custom_rich_dialog_button_bg_selecter"
            android:paddingLeft="12dp"
            android:paddingTop="10dp"
            android:paddingRight="12dp"
            android:paddingBottom="10dp"
            android:text="@string/dcloud_privacy_prompt_button_visitor_mode"
            android:textColor="@drawable/dcloud_custom_rich_dialog_button_text_selecter"
            android:textSize="14sp"
            android:visibility="gone" />


    </LinearLayout>

</LinearLayout>