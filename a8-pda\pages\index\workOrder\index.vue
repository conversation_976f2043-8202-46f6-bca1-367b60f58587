<script>
// import { GetListAPI, GetStatusAPI } from "@/api/index/workOrder";
import { mapGetters } from "vuex";

export default {
  props: ["type", "condition", "pageNum"],
  computed: {
    ...mapGetters(["getRoleType"]),
  },
  data() {
    return {
      list: [
        {
          name: "待处理",
          status: 0,

          color: "#e85a58",
        },

        {
          name: "已处理",
          status: 1,
          color: "#9bdb72",
        },
        {
          name: "已发起",
          status: 2,
          color: "#bacef1",
        },
        {
          name: "暂停",
          status: 3,
          color: "#c9c0bd",
        },
        {
          name: "抄送",
          status: 4,
          color: "#5dd0d2",
        },
      ],
      baseList: [
        {
          icon: "../../../static/images/sy.png",
          title: "首页",
          url: "/pages/index/index",
        },
        {
          icon: "../../../static/images/spzx.png",
          title: "审批中心",
          url: "1",
        },
        {
          icon: "../../../static/images/tj.png",
          title: "统计",
          url: "1",
        },
      ],
      // 分页查询
      query: {
        pageNum: 1,
        pageSize: 5,
        //  是否数据隔离
        status: 0,
      },
      // 加载状态
      status: "loading",
      //   列表数据
      tableData: [],
      total: 0,
      // 字典
      dicts: {},
      // 筛选弹窗
      screenShow: false,
      // 提交审核告警弹窗
      subExamineShow: false,
      // 转单弹窗
      transferShow: false,
      // 派工弹窗
      workShow: false,
      // 勾选后的数据
      checkArr: [],
      // 勾选后的状态
      statusArr: [],
      // 派工加载
      workLoading: false,
      selectedValue: "2",
      teamGroupList: [
        {
          text: "张三",
          value: "1",
        },
        {
          text: "张5",
          value: "2",
        },
      ],
      // 已选择的派工人显示
      valueShow: [
        {
          label: "张三",
          value: "1",
        },
        {
          label: "张5",
          value: "2",
        },
        {
          label: "张三",
          value: "1",
        },
        {
          label: "张5",
          value: "2",
        },
        {
          label: "张三",
          value: "1",
        },
        {
          label: "张5",
          value: "2",
        },
        {
          label: "张三",
          value: "1",
        },
        {
          label: "张5",
          value: "2",
        },
      ],
      // 已选择的派工人
      workCheckValue: [],
      indixAcita: "0",
      userValue: [],
      checkboxValue: [],
      // 派工人列表
      workList: [],
    };
  },
  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      this.tableData = [];
      this.status = "loading";
      this.query.pageNum = 1;
      await this.getList();
      // const { data: res } = await GetStatusAPI();
      // this.list = res;
    } catch (err) {
    } finally {
      uni.stopPullDownRefresh();
    }
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  async onReachBottom() {},
  async onLoad() {
    // const { data: res } = await GetStatusAPI();
    // this.list = res;
  },
  onShow() {
    this.screenComplete();
  },
  methods: {
    /**
     * 标签页切换时触发 ( 0:全部 1:待派工 2:维保中 3:待审核 4:维保完成 )
     * @param {*} item
     */
    tabsChange(item) {
      this.query.status = item.status;

      this.screenComplete();
    },

    /**
     * 筛选重置
     */
    async screenReset() {
      this.screenShow = false;
      this.query.mineType = 0;
      this.tableData = [];
      this.query.pageNum = 1;
      this.status = "loading";
      await this.getList();
    },
    /**
     * 筛选完成
     */
    async screenComplete() {
      // this.query.pageNum = 1;
      // this.status = "loading";
      // this.tableData = [];
      // const { data: res } = await GetStatusAPI();
      // this.list = res;
      // this.getList();
    },
    /**
     * 手动下拉
     */
    async scrolltolower() {
      if (this.status == "nomore") return;
      this.status = "loading";
      this.query.pageNum = ++this.query.pageNum;
      await this.getList();
    },

    /**
     * tabs切换
     */
    tabsChane(v) {
      this.indixAcita = v.status;
      this.query.status = v.status;

      this.search();
    },
    /**
     * 搜索
     */
    search() {
      this.query.pageNum = 1;
      this.query.pageSize = 5;

      this.status = "loading";
      this.tableData = [];
      this.getList();
    },
    /**
     * 获取维保列表
     */
    async getList() {
      const { data: result } = await GetListAPI(this.query);
      /**
       * 解决页面不更新
       */
      this.$nextTick(() => {
        this.tableData = this.tableData.concat(result.result);

        this.total = result.total;
        console.log(
          this.tableData.length >= this.total,
          "this.tableData.length >= this.total",
          this.tableData
        );
        if (this.tableData.length >= this.total) {
          this.status = "nomore";
        }
      });
    },
    /**
     * 跳转
     * @param {*} val
     */
    toPath(val) {
      if (val == 1) {
        // uni.navigateTo({
        //   delta: val,
        // });
      } else {
        uni.switchTab({
          url: "/pages/index/index",
        });
      }
    },
  },

  /**
   * 监听从搜索页的搜索条件
   */
  watch: {
    condition: {
      handler(n, o) {
        if (typeof n != "undefined") {
          this.query.queryParam = n;
          this.getList();
        }
      },
      immediate: true,
    },
    /**
     * 监听搜索分页 pageNum
     */
    pageNum: {
      handler(n, o) {
        if (this.type == "搜索" && n != 1) {
          this.query.pageNum = n;
          this.getList();
        }
      },
      immediate: true,
    },
    /**
     * 监听页面分页
     */
    pageNumChange: {
      handler(n, o) {
        console.log(n, "分页");
        if (n == 1) {
          this.$emit("pageChange");
        }
      },
      immediate: true,
    },
  },
};
</script>

<template>
  <view class="page">
    <view>
      <u-navbar
        title="任务列表"
        bgColor="#5375ff"
        placeholder
        titleStyle="color:#ffffff"
        height="44px"
      >
      </u-navbar>
      <!-- <u-sticky> -->

      <view class="tabs">
        <view
          class="tabs-list"
          v-for="(t, i) in list"
          :class="[indixAcita == t.status ? 'indixAcita' : '']"
          @click="tabsChane(t)"
          :style="`color:${t.color}`"
          :key="i"
        >
          {{ t.name }}
          <u-badge
            :type="type"
            class="badge"
            max="99"
            :value="t.num"
            v-if="t.name == '待处理'"
          ></u-badge>
        </view>
      </view>
      <view class="search">
        <u-search
          shape="round"
          v-model="query.queryParam"
          :clearabled="true"
          placeholder="请输入部门关键字"
          @search="search"
          @custom="search"
        ></u-search>
      </view>
    </view>
    <!-- </u-sticky> -->

    <view class="box">
      <u-list @scrolltolower="scrolltolower">
        <u-list-item v-for="(t, i) in tableData" :key="i">
          <p-card border class="info-box">
            <template #header>
              <view class="flex-between">
                <view class="flex-items">
                  <!-- <p-tag :text="t.status" /> -->
                  <view class="titles ellipsis"
                    >{{ t.initiatorName }}提交的安环稽查</view
                  >
                </view>
                <view style="display: flex">
                  <!-- 工单状态为“待维保、维保中、待提交、未通过”时高亮可点击 -->
                  <u-tag
                    @click="
                      indixAcita == 0
                        ? $u.navigateTo(
                            '/pages/index/workOrder/details?id=' + t.id
                          )
                        : $u.navigateTo(
                            '/pages/index/workOrder/detailsCopy?id=' + t.id
                          )
                    "
                    size="mini"
                    text="详情"
                    plain
                    style="margin-right: 10px"
                    plainFill
                    type="primary"
                  />
                  <u-tag
                    @click="$u.navigateTo('/pages/components/make?id=' + t.id)"
                    size="mini"
                    text="抄送"
                    plain
                    plainFill
                    v-if="
                      getRoleType == 1 && indixAcita == 2 && t.copyStatus == 1
                    "
                    type="primary"
                  />
                </view>
              </view>
            </template>

            <view class="content">
              <view>
                <u-cell title="任务单号：" isLink :border="false">
                  <text slot="value" class="u-slot-value">{{ t.taskNo }}</text>
                </u-cell>
                <u-cell title="日期：" isLink :border="false">
                  <text slot="value" class="u-slot-value">{{
                    t.auditTime
                  }}</text>
                </u-cell>
                <u-cell title="部门：" isLink :border="false">
                  <text slot="value" class="u-slot-value">{{
                    t.deptName
                  }}</text>
                </u-cell>
                <u-cell title="具体位置：" isLink :border="false">
                  <text slot="value" class="u-slot-value">{{
                    t.location
                  }}</text>
                </u-cell>
                <u-cell title="隐患类型：" isLink :border="false">
                  <text slot="value" class="u-slot-value">{{
                    t.hazardTypeDict.dictLabel || "--"
                  }}</text>
                </u-cell>
                <u-cell title="隐患类别：" isLink :border="false">
                  <view slot="value" class="u-slot-value">
                    <u-tag
                      :text="t.hazardCategoryDict.dictLabel"
                      :bgColor="t.hazardCategoryDict.cssClass"
                      :borderColor="t.hazardCategoryDict.cssClass"
                      type="warning"
                    ></u-tag>
                  </view>
                </u-cell>

                <view></view>
              </view>
            </view>
          </p-card>
        </u-list-item>
        <u-loadmore :status="status" />
      </u-list>
    </view>

    <!-- 底部操作 -->
    <!-- <p-bottom> -->
    <view class="bottom">
      <u-grid :border="false">
        <u-grid-item
          v-for="(baseListItem, baseListIndex) in baseList"
          :key="baseListIndex"
          @click="toPath(baseListItem.url)"
          style="position: relative"
        >
          <view v-if="baseListItem.title == '审批中心'">
            <u-badge
              :type="type"
              style="position: absolute; top: 0; right: 30%"
              max="99"
              :value="list.filter((t) => t.name == '待处理')[0].num"
            ></u-badge>
          </view>
          <p-svg :src="baseListItem.icon" style="margin-bottom: 10px" />
          <text class="grid-text">{{ baseListItem.title }}</text>
        </u-grid-item>
      </u-grid>
    </view>
    <!-- </p-bottom> -->
  </view>
</template>

<style lang="scss" scoped>
.flex-items {
  width: 180px;

  .title {
    margin-left: 10px;
  }
}

.titles {
  min-width: 100px;
  max-width: 170px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin: 0 5px;
}

/deep/ .u-search__action {
  // color: #fff !important;
}

.page {
  height: calc(100vh - 0px);
  background-color: #5375ff;

  display: flex;
  flex-direction: column;
}

.box {
  box-sizing: border-box;
  background: #f1f3f7;
  padding-top: 10rpx;
  // min-height: 100%;
  border-radius: 30rpx;
  flex: 1;
  overflow-y: auto;
  padding-bottom: 150rpx;
}

.indixAcita {
  border: 1px solid #fff;
  border-radius: 35rpx;
}

.search {
  width: 80%;
  margin: 10rpx auto;
}

.bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  height: 150rpx;
  width: 100%;
  border-top: 1px solid #747474;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tabs {
  height: 100rpx;
  display: flex;
  justify-content: space-evenly;
  background-color: #5375ff;
  align-items: center;

  .tabs-list {
    color: #fff;
    padding: 1rpx 0rpx;
    width: 17%;
    display: flex;
    font-size: 25rpx;
    align-items: center;
    height: 50%;
    justify-content: center;
    position: relative;

    .badge {
      top: 0;
      position: absolute;
      right: -10%;
    }
  }
}

.top {
  background-color: #5375ff;
}

/deep/ .u-icon__icon {
  display: none !important;
}

/deep/ .u-navbar__content {
  // background-color: rgba(0, 0, 0, 0) !important;
}

.info-box {
  margin-top: 20px;
}

.content {
  width: 100%;
  margin-top: 20px;

  .content-item {
    display: flex;
    align-items: center;
    margin-left: 10px;
    width: 100%;

    .label {
      color: #747474;
    }
  }
}

.bottom-items {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  align-items: center;
}

.tabsBox {
  background-color: #ffffff;
  border: 1px solid #000;
  border-left: 0 solid #000;
  border-right: 0 solid #000;
  position: relative;

  .screen-box {
    position: absolute;
    width: 100vw;
    top: 40px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;

    .screen-content {
      height: 100px;
      width: 100vw;
      padding: 20px;
      background-color: #ffffff;
      border-bottom: 1px solid #000;

      .screen-my {
        width: 80px;
        height: 10px;
        color: #fff;
        padding: 10px;
        cursor: pointer;
        line-height: 10px;
        text-align: center;
        border-radius: 5px;
        background: #d1d1d1;
        border: 1px solid #d1d1d1;
      }

      .screen-my-av {
        background: #007aff;
        border: 1px solid #007aff;
      }
    }

    .screen-bottom {
      display: flex;
      justify-content: space-between;
    }
  }
}

.workBy {
  border: 1px solid #ebebeb;
  min-height: 30px;
  min-width: 200px;
  padding: 5px;
  margin-top: 10px;
  border-radius: 5px;
  flex-wrap: wrap;

  .workByItem {
    padding: 0 5px;
    margin: 0 5px;
    margin-bottom: 5px;
    border-radius: 5px;
    background-color: #e9f5fd;
    color: #239ceb;
  }
}
</style>