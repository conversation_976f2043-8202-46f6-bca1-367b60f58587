<template>
	<z-paging ref="paging" v-model="dataList" @query="query" @virtualListChange="virtualListChange" use-virtual-list
		:force-close-inner-list="true" cell-height-mode="dynamic">
		<view>
			<u-navbar title="调拨入库" @leftClick="toPath()" bgColor="#4669ea" placeholder></u-navbar>
		</view>

		<view style="display: flex;gap: 10rpx;align-items: center;margin: 10rpx;">
			<uni-easyinput style="flex: 4;" v-model="queryData.transferOrderNo" placeholder="请输入调拨单号搜索" border="surround"
				prefixIcon="scan" @iconClick="scanCode" @clear="clear">
			</uni-easyinput>

			<u-button style="flex: 1;" size="small" type="success" text="搜索" @click="search"></u-button>
		</view>

		<view :id="`zp-id-${item.zp_index}`" :key="item.zp_index" v-for="(item, index) in virtualList">
			<uni-card :title="'调拨单号：'+item.transferOrderNo" style="border-radius: 20rpx">
				<template #title>
					<view
						style="display: flex;justify-content: center;align-items: center;margin-top: 5px;margin-left: 5px">
						<text>调拨单号：{{item.transferOrderNo}}</text>
						<u-button :customStyle="btnCustomStyle" type="error" text="同步ERP" @click="synchronous(item.transferOrderNo)"></u-button>
						<u-button :customStyle="btnCustomStyle1" type="success" text="详情" @click="details(item)"></u-button>
					</view>

				</template>

				<view style="display: flex;justify-content: space-between">
					<text>创建时间: </text>
					<text>{{item.documentCreateTime}}</text>
				</view>
				<view style="display: flex;justify-content: space-between">
					<text>创建人: </text>
					<text>{{item.documentCreateUser}}</text>
				</view>
				<view style="display: flex;justify-content: space-between">
					<text>调拨备注: </text>
					<text>{{item.remark}}</text>
				</view>

			</uni-card>
		</view>
		<u-toast ref="toastRef"></u-toast>
	</z-paging>
</template>

<script>
	import {GetTransferInListAPI, InboundConfirmAPI} from "@/api/store/inWarehouse/inWarehouse"
	export default {
		data() {
			return {
				dataList: [],
				queryData: {},
				virtualList: [],
				btnCustomStyle: {
					'margin-bottom': '15rpx',
					width: '70px',
					height: '30px',
					marginRight: '10rpx',
				},
				btnCustomStyle1: {
					'margin-bottom': '15rpx',
					width: '50px',
					height: '30px',
					marginLeft: '10rpx',
					marginRight: '10rpx'
				}
			};
		},
		methods: {
			search() {
				this.$refs.paging.reload();
			},
			clear() {
				this.queryData = [],
				this.$refs.paging.reload();
			},
			synchronous(val) {
				InboundConfirmAPI({transferOrderNo: val}).then(res =>{
					if(res.code === 200) {
						this.$refs.toastRef.show({
							type: "success",
							message: "同步ERP成功。"
						})
						this.$refs.paging.reload();
					}
				})
			},
			scanCode() {
				const that = this
				uni.scanCode({
					onlyFromCamera: true,
						success: function (res) {
							that.$set(that.queryData, "transferOrderNo", res.result);
						},
						fail() {
							that.$refs.toastRef.show({
								type: "error",
								message: "识别失败，请重新识别。"
							})
						}
				})
			},
			query(pageNo, pageSize) {
				let params = {}
				const {transferOrderNo} = this.queryData
				if(transferOrderNo) {
					params.transferOrderNo = transferOrderNo
				}
				GetTransferInListAPI(pageNo, pageSize, params).then(res => {
					this.virtualList = res.rows;
					this.$refs.paging.complete(res.rows);
				}).catch(res => {
					this.$refs.paging.complete(false);
				})
				
			},
			virtualListChange(vList) {
				this.virtualList = vList;
			},
			//返回
			toPath() {
				uni.navigateBack({
					delta: 1
				})
			},
			details(row) {
				uni.navigateTo({
							url: `/pages/store/inWarehouse/stockTransfer/inWarehouse/details`,
							success: function(res) {
								res.eventChannel.emit('rowData', row)
							}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.uni-body {
		font-size: 14px;
		font-weight: 400;
		line-height: 22px
	}

	.card-body-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 15rpx;
		font-family: 'PingFang-SC-Light';
	}

	.search {
		width: 93%;
		margin: 10rpx 10rpx 10rpx 12rpx;
		display: flex;
		flex-direction: column;
		gap: 15rpx;
		background-color: #ffffff;
		padding: 15rpx;
		border-radius: 20rpx;
		box-shadow: 0 10rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.search-row {
		display: flex;
		gap: 10rpx;
	}

</style>