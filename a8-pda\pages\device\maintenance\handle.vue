<script>
  // import {
  //   GetViewAPI,
  //   AddSafeAPI,
  //   AddSecurityAPI,
  //   PauseAPI,
  //   GetTurnoverAPI,
  // } from "@/api/index/workOrder";
  import {
    GetViewAPI,
    repairHandleAPI,
    nexusAPI,
    GetTurnoverAPI,
  } from "@/api/index/maintenance";
  import { getTime } from "@/utils/funComm";
  import { useDict } from "@/utils/dict";

  import { mapGetters } from "vuex";
  import { upload } from "@/utils/request";
  export default {
    computed: {
      ...mapGetters(["getInfo"]),
    },
    data() {
      return {
        id: "",
        fileList: [],
        title: "",
        isShow: false,
        range: [],
        fault_classification: [],
        rules: {
          faultAnalysis: {
            type: "string",
            required: true,
            message: "请填写故障分析",
            trigger: ["blur", "change"],
          },
          // fillAccessories: {
          //   type: "string",
          //   required: true,
          //   message: "请填写维修配件",
          //   trigger: ["blur", "change"],
          // },
          nexusReason: {
            type: "string",
            required: true,
            message: "请填写关结原因",
            trigger: ["blur", "change"],
          },
          turnReason: {
            type: "string",
            required: true,
            message: "请填写驳回原因",
            trigger: ["blur", "change"],
          },
          // repairType: {
          //   type: "string",
          //   required: true,
          //   message: "请选择维修分类",
          //   trigger: ["blur", "change"],
          // },
          breakdownType: {
            type: "string",

            required: true,
            message: "请选择故障分类",
            trigger: ["blur", "change"],
          },
        },
        form: { recoveryTime: "", itemList: [] },
        status: "",
        list: [],
        list2: [],

        show: false,
        showCll: false,
        tabs: [
          {
            name: "申请信息",
          },
          {
            name: "流转信息",
          },
        ],
        tabsIndex: 0,
        column: [
          { label: "设备编号", prop: "deviceNo" },
          { label: "设备名称", prop: "deviceName" },
          {
            label: "设备类型",
            prop: "deviceType",
          },
          { label: "产线", prop: "productionLineName" },

          { label: "报修人", prop: "repairName" },

          { label: "规格型号", prop: "type" },
          // {
          //   label: "维修分类",
          //   prop: "repairTypeName",
          // },
          // { label: "故障类型", prop: "breakdownTypeName" },
        ],
        column2: [
          { label: "物料描述", prop: "materielName", width: "aotu" },
          { label: "数量", prop: "quantity" },
        ],
      };
    },
    onLoad(option) {
      this.id = option.id;
      this.dict();
      this.getData(option.id);
    },
    methods: {
      /**
       *详情
       */
      async getData(id) {
        try {
          const { data: res } = await GetViewAPI(id);
          this.getTurnover(res.fillCode);
          res.handleName = this.getInfo.userName;
          res.handleTime = getTime();
          res.fillPicture = JSON.parse(res.fillPicture);
          this.form = res;
          console.log(this.form);
        } catch { }
      },

      /**
       * 流转信息
       */

      async getTurnover(id) {
        const { data: res } = await GetTurnoverAPI(id);
        this.list2 = res;
      },

      /**
       * 切换
       */
      click(i) {
        this.tabsIndex = i;
      },
      // 删除图片
      deletePic(event) {
        this[`fileList`].splice(event.index, 1);
      },

      // 新增图片
      async afterRead(event) {
        // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
        let lists = [].concat(event.file);
        let fileListLen = this[`fileList`].length;
        lists.map((item) => {
          this[`fileList`].push({
            ...item,
            status: "uploading",
            message: "上传中",
          });
        });
        console.log(lists, "lists");
        for (let i = 0; i < lists.length; i++) {
          try {
            const { data: result } = await upload(lists[i].url);
            console.log(result, "result");

            let item = this[`fileList`][fileListLen];
            this[`fileList`].splice(
              fileListLen,
              1,
              Object.assign(item, {
                status: "success",
                message: "",
                url: result.url,
              })
            );

            fileListLen++;
          } catch (err) { }
        }
      },

      /**
       * 跳转
       * @param {*} val
       */
      toPath(val) {
        if (val != "/pages/index/index") {
          uni.navigateBack({
            delta: 1,
          });
        } else {
          uni.switchTab({
            url: "/pages/index/index",
          });
        }
      },
      /**获取字典 */
      async dict() {
        this.range = await useDict("maintenance_classification");
        this.fault_classification = await useDict("fault_classification");
      },
      /**备件选择确认 */
      materialChange(val) {
        if (val.length > 0) {
          val.map((t) => (t.quantity = 1));
        }
        this.form.itemList = val;
        this.show = false;
      },

      /**处理*/
      async submit() {
        const res = await this.$refs.uFormTwo.validate();
        if (!res) return;

        this.form.fillPicture = JSON.stringify(this.form.fillPicture);
        this.form.handlePicture = JSON.stringify(
          this.fileList.length == 0 ? [] : this.fileList.map((t) => t.url)
        );
        this.form.status = 3;
        await repairHandleAPI(this.form);
        uni.showToast({
          title: "处理成功",
          icon: "checkmark",
        });
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
          });
        }, 1000);
      },
      /**关结 */
      async nexusSubmit() {
        const res = await this.$refs.uForm.validate();
        if (!res) return;
        this.form.status = 5;
        this.form.fillPicture = JSON.stringify(this.form.fillPicture);
        this.form.handlePicture = JSON.stringify(
          this.fileList.length == 0 ? [] : this.fileList.map((t) => t.url)
        );
        this.form.repairResult = 4;
        await nexusAPI(this.form);

        uni.showToast({
          title: "关结成功",
          icon: "checkmark",
        });
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
          });
        }, 1000);
      },

      /**驳回 */
      async rejectSubmit() {
        const res = await this.$refs.uForm.validate();
        if (!res) return;
        this.form.fillPicture = JSON.stringify(this.form.fillPicture);
        this.form.handlePicture = JSON.stringify(
          this.fileList.length == 0 ? [] : this.fileList.map((t) => t.url)
        );
        this.form.status = 1;

        await nexusAPI(this.form);
        uni.showToast({
          title: "驳回成功",
          icon: "checkmark",
        });
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
          });
        }, 1000);
      },
    },
  };
</script>
<template>
  <view class="look">
    <u-navbar title="设备维修处理" @leftClick="toPath(1)" bgColor="#ffffff" placeholder>
    </u-navbar>
    <view class="tabs">
      <view :class="[tabsIndex == i ? 'tabsIndex' : '']" class="tabs-item" v-for="(t, i) in tabs" :key="i"
        @click="click(i)">{{ t.name }}</view>
    </view>

    <view v-if="tabsIndex == 0" class="content">
      <!-- 填报内容 -->
      <view class="content_title"> 设备维修单号：{{ form.fillCode }}</view>
      <view class="list">
        <u-cell v-for="(item, index) in column" :key="index" class="list_item" :title="item.label + ':'"
          :border="false">
          <text slot="value" class="ellipsis-text" v-if="item.type != '自定义'">{{ form[item.prop] }}</text>
        </u-cell>
      </view>
      <u-cell class="list_items" title="报修时间:" :border="false">
        <text slot="value" class="ellipsis-text" style="width: 136px">{{
          form.repairTime
          }}</text>
      </u-cell>

      <view class="flex" style="padding: 5px 10px">
        <text class="text">故障描述：</text>
        <u--textarea v-model="form.breakdownDescription" count :maxlength="255" disabled placeholder="请输入故障描述">
        </u--textarea>
      </view>
      <view class="flex" style="padding: 5px 10px">
        <text class="text">图片:</text>
        <u-album :urls="form.fillPicture" singleSize="80" multipleSize="80"></u-album>
      </view>
      <!-- 处理内容 -->
      <view class="list">
        <view class="list-item" style="width: 40%">
          维修人： {{ form.handleName }}</view>
        <view class="list-item" style="width: 60%">
          维修时间：{{ form.handleTime }}
        </view>
      </view>
      <u--form :model="form" :rules="rules" ref="uFormTwo" borderBottom labelWidth="80">
        <u-form-item label="维修分类:" prop="repairType" ref="item1" labelWidth="80">
          <uni-data-select v-model="form.repairType" :localdata="range" @change="change"></uni-data-select>
        </u-form-item>
        <u-form-item label="故障分类:" ref="item1" labelWidth="80">
          <uni-data-select v-model="form.breakdownType" :localdata="fault_classification" @change="change">
          </uni-data-select>
        </u-form-item>
        <u-form-item prop="faultAnalysis" style="width: 100%" :required="true" label="故障分析">
          <u--textarea v-model="form.faultAnalysis" placeholder="请输入故障分析" count style="width: 300rpx" maxlength="150">
          </u--textarea>
        </u-form-item>
        <u-form-item label="维修配件:" prop="fillAccessories" ref="item1" labelWidth="80">
          <u-input placeholder="维修配件" v-model="form.fillAccessories">
          </u-input>
        </u-form-item>
        <u-form-item ref="item1" borderBottom>
          <view style="width: 30%"><text class="disabled">图片: </text></view>
          <view style="flex: 1">
            <view class="p-upload">
              <u-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic" name="file" multiple
                :maxCount="9">
                <p-svg size="60" src="../../../static/SVG/upload.svg" />
              </u-upload>
            </view>
          </view>
        </u-form-item>
      </u--form>
      <!-- 备件 -->
      <!-- <view style="float: right; margin: 20rpx 0">
        <u-button
          type="primary"
          style="height: 60rpx"
          text="添加备件"
          @click="show = true"
          color="#02a7f0"
        ></u-button>
      </view>
      <p-table :list="form.itemList" :column="column2" ref="tableRef">
      </p-table> -->
    </view>
    <!-- 流转信息 -->
    <view v-else style="height: 80%">
      <p-circulation :list="list2" :fillCode="form.fillCode"></p-circulation>
    </view>

    <view class="bottom" v-if="tabsIndex == 0">
      <u-button type="primary" style="width: 25%; height: 60rpx" text="关结" @click="
          () => {
            isShow = true;
            title = '关结';
          }
        " color="#5ac725"></u-button>
      <u-button type="primary" style="width: 25%; height: 60rpx" color="#e74f4c" @click="
          () => {
            isShow = true;
            title = '驳回';
          }
        " text="驳回"></u-button>
      <u-button type="primary" style="width: 25%; height: 60rpx" @click="submit()" text="处理"></u-button>
    </view>
    <!-- 选择物料 -->
    <u-popup :show="show" @close="show = false" @open="open" :closeable="true">
      <p-material :title="'备件选择'" @change="materialChange"></p-material>
    </u-popup>

    <u-modal :show="isShow" :title="title" :showConfirmButton="true" :showCancelButton="true"
      @confirm="title == '关结' ? nexusSubmit() : rejectSubmit()" @cancel="isShow = false">
      <view style="width: 100%">
        <u--form :model="form" :rules="rules" ref="uForm" borderBottom labelWidth="80">
          <u-form-item v-if="title == '关结'" prop="nexusReason" style="width: 100%" :required="true" label="关结原因">
            <u--textarea v-model="form.nexusReason" placeholder="请输入关结原因" count style="width: 300rpx" maxlength="150">
            </u--textarea>
          </u-form-item>
          <u-form-item v-if="title == '驳回'" prop="turnReason" style="width: 100%" :required="true" label="驳回原因">
            <u--textarea v-model="form.turnReason" placeholder="请输入驳回原因" count style="width: 300rpx" maxlength="150">
            </u--textarea>
          </u-form-item>
        </u--form>
      </view>
    </u-modal>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<style lang="scss" scoped>
  .look {
    background-color: #fff;
    // height: 100%;
    min-height: 100vh;
    // overflow-y: auto;
  }

  .content {
    padding: 20px;
    //   background-color: #f3f2f2;
  }

  .list {
    display: flex;
    justify-content: space-around;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .list-item {
    font-size: 12px;
    margin-bottom: 10rpx;
    overflow: hidden;
  }

  .not-fold {
    transition: all 0.2s;
    height: 0px;
  }

  .text {
    font-size: 12px;
    width: 120rpx;
  }

  .is-fold {
    transition: all 0.2s;
    height: 55vh;
    overflow: hidden;
    overflow-y: auto;
    padding: 10rpx;
  }

  .record {
    border: 1px solid #d1d1d1;
  }

  .list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .list_item {
    width: 50%;
    font-size: 12px;
  }

  .list_items {
    width: 70%;
    font-size: 12px;
  }

  .ellipsis-text {
    width: 50%;
    white-space: nowrap;
    // font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .content_title {
    margin-bottom: 10rpx;
    font-size: 14px;
  }

  .tabs-item {
    width: 50%;
    height: 70rpx;
    line-height: 70rpx;
    text-align: center;
  }

  .tabsIndex {
    background-color: #2e8ff4;
    color: #fff;
  }

  /deep/ .u-cell__body {
    padding: 0px 10px !important;
    font-size: 12px;
  }

  /deep/ .u-cell__title {
    flex: 0.8;
  }

  /deep/ .u-cell__title-text {
    font-size: 12px;
  }

  .bottoms {
    position: fixed;
    bottom: 0;
    left: 0;
    height: 150rpx;
    width: 100%;
    border-top: 1px solid #747474;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .tabs {
    height: 70rpx;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }

  .bottom {
    display: flex;
    height: 60rpx;
    //   margin-top: 20rpx;
  }

  .flex {
    display: flex;
    margin-bottom: 5rpx;
  }

  .form {
    padding: 20rpx;
  }
</style>