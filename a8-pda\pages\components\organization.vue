<script>
import { GetHandOverTreeUserSelectAPI, GetOvertAPI } from "@/api/common";
export default {
  props: ["teamObj"],

  data() {
    return {
      tree: [],
      max: 5,
      id: "",
    };
  },

  methods: {
    /**
     * 提交
     */
    async submit(v) {
      console.log(v, "v");
      let careBy = v.map((t) => t.id);
      console.log(careBy);
      const res = await GetOvertAPI({
        id: this.id,
        careByIds: careBy,
      });
      if (res.code == 200) {
        uni.showToast({
          title: "转交成功",
          duration: 2000,
        });
        setTimeout(() => {
          uni.navigateTo({
            url: "/pages/index/workOrder/index",
          });
        }, 500);
      }
    },

    checkboxGroupChange(v) {
      console.log(v, "vsss");
    },
    /**
     * 跳转
     * @param {*} val
     */
    toPath(val) {
      if (val == 1) {
        uni.navigateBack({
          delta: val,
        });
      } else {
        uni.navigateTo({
          url: val,
        });
      }
    },
  },

  onLoad(option) {
    console.log("this.getRoleType", option);
    this.id = option.id;
  },
  /**
   * 获取班组下拉
   */
  async mounted() {
    const { data: res } = await GetHandOverTreeUserSelectAPI();
    this.tree = res;
  },
};
</script>
<template>
  <view class="organization">
    <u-navbar
      title="转交"
      @rightClick="toPath(1)"
      @leftClick="toPath(1)"
      v-if="type != '搜索'"
      rightIcon="icon-sousuo"
      bgColor="#ffffff"
      placeholder
    >
      <template #right>
        <view> 取消</view>
      </template>
    </u-navbar>

    <luyj-tree
      v-slot:default="{ item }"
      @sendValue="submit"
      :trees="tree"
      :is-check="true"
      :props="{
        id: 'id',
        label: 'label',
        children: 'children',
        multiple: true,
      }"
    >
      <!-- 内容插槽 -->
      <view>
        <view class="content-item">
          <!-- <u-checkbox-group v-if="item.pa">
            <view class="word">
              <u-checkbox
                @change="checkboxGroupChange"
                v-model="item.checked"
                :name="item.label"
                >{{ item.label }}</u-checkbox
              >{{ item.label }}
            </view>
          </u-checkbox-group> -->

          <view class="word">{{ item.label }} </view>
        </view>
      </view>
    </luyj-tree>
  </view>
</template>

<style lang="scss" scoped>
.organization {
  height: 100vh;
  background-color: #fff;
}
.word {
  display: flex;
}
.title {
  width: 100% !important;
}
</style>