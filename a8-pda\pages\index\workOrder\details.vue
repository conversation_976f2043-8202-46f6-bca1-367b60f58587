<script>
import {
  GetViewAPI,
  AddSafeAPI,
  AddSecurityAPI,
  PauseAPI,
  GetTurnoverAPI,
} from "@/api/index/workOrder";
import { mapGetters } from "vuex";
import { upload } from "@/utils/request";
export default {
  computed: {
    ...mapGetters(["getRoleType"]),
  },
  data() {
    return {
      rules: {
        actualInstructionName: {
          type: "string",
          required: true,
          message: "请选择实际指令",
          trigger: ["blur", "change"],
        },
      },
      id: "",
      deletable: false,
      fileList: [],

      //   传入卡片的keys
      keys2: [
        {
          label: "回复时间",
          prop: "recoveryTime",
        },
        {
          label: "整改状态",
          prop: "rectificationState",
        },
        // {
        //   label: "整改回复",
        //   prop: "rectificationReply",
        // },
        {
          label: "计划时间",
          prop: "plannedCompletionTime",
        },
        {
          label: "结果跟踪",
          prop: "approvalOpinion",
        },
        {
          label: "审核状态",
          prop: "approvalStatus",
        },
        {
          label: "照片上传",
          prop: "imageUrls",
        },
      ],
      keys3: [
        {
          label: "销号时间",
          prop: "cancellationTime",
        },
        {
          label: "核查销号",
          prop: "verifyCancel",
        },
        // {
        //   label: "安环回复",
        //   prop: "safetyRecovery",
        // },
        {
          label: "限期整改",
          prop: "rectificationTime",
        },
        {
          label: "结果跟踪",
          prop: "approvalOpinion",
        },
        {
          label: "审核状态",
          prop: "approvalStatus",
        },
      ],
      keys: [
        {
          label: "任务单号",
          prop: "taskNo",
        },
        {
          label: "稽查时间",
          prop: "auditTime",
        },
        {
          label: "班组",
          prop: "deptName",
        },
        {
          label: "位置",
          prop: "location",
        },
        {
          label: "隐患类型",
          prop: "hazardType",
        },
        {
          label: "隐患级别",
          prop: "hazardCategory",
        },
        {
          label: "隐患描述",
          prop: "hazardDescription",
        },
        {
          label: "整改要求",
          prop: "repairDemand",
        },
        {
          label: "限期整改",
          prop: "rectificationTime",
        },
        {
          label: "照片上传",
          prop: "imageUrls",
        },
      ],
      rules: {
        recoveryTime: {
          type: "string",
          required: true,
          message: "请选择回复时间",
          trigger: ["blur", "change"],
        },
        rectificationState: {
          type: "string",
          required: true,
          message: "请选择整改状态",
          trigger: ["blur", "change"],
        },
        // rectificationReply: {
        //   type: "string",
        //   required: true,
        //   message: "请输入整改回复",
        //   trigger: ["blur", "change"],
        // },
        plannedCompletionTime: {
          type: "string",
          required: true,
          message: "请选择计划时间",
          trigger: ["blur", "change"],
        },
        cancellationTime: {
          type: "string",
          required: true,
          message: "请选择销号时间",
          trigger: ["blur", "change"],
        },
        verifyCancel: {
          type: "string",
          required: true,
          message: "请输入核查销号",
          trigger: ["blur", "change"],
        },
        safetyRecovery: {
          type: "string",
          required: true,
          message: "请填写安环回复",
          trigger: ["blur", "change"],
        },
        rectificationTime: {
          type: "string",
          required: true,
          message: "请选择限期整改复",
          trigger: ["blur", "change"],
        },
        approvalOpinion: {
          type: "string",
          required: true,
          message: "请填写结果跟踪",
          trigger: ["blur", "change"],
        },
      },

      form: { recoveryTime: "" },
      formTwo: { approvalOpinion: "" },
      status: "",
      list: [],
      list2: [],
      baseList: [
        {
          icon: "../../../static/images/sy.png",
          title: "首页",
          url: "/pages/index/index",
        },
        {
          icon: "../../../static/images/back.png",
          title: "返回",
          url: "/pages/index/workOrder/index",
        },
      ],
      taskHistory: [],
      hazardCategory: "",

      show: false,
      showCll: false,
      tabs: [
        {
          name: "申请信息",
        },
        {
          name: "流转信息",
        },
      ],
      tabsIndex: 0,
      value: ["0"],
      // 维保项目数据
      maintenanceData: {
        name: "保养人员数据",
      },
      radiolist1: [
        {
          name: "1",
          label: "是",
          disabled: false,
        },
        {
          name: "0",
          label: "否",
          disabled: false,
        },
      ],
      actions: [],
      rectificationStatesDic: [],
    };
  },
  onLoad(option) {
    this.id = option.id;
    console.log("this.getRoleType", this.getRoleType);
    this.getData(option.id);
    this.getTurnover(option.id);
    this.getTime();
    // this.getTime3();
  },
  methods: {
    /**
     *详情
     */
    async getData(id) {
      const { data: res } = await GetViewAPI(id);
      this.rectificationStatesDic = res.rectificationStatesDic.map((t) => {
        return {
          text: t.dictLabel,
          value: t.dictValue,
          dictValue: t.dictValue,
        };
      });

      res.taskHistory.map((t) => {
        t.values = t.type;
      });
      this.status = res.status;

      this.list = res.taskHistory;
      this.list.map((t) => {
        if (t.dataMsg.imageUrls) {
          console.log(JSON.parse(t.dataMsg.imageUrls));
          t.dataMsg.imageUrls = JSON.parse(t.dataMsg.imageUrls);
        }
      });
      this.getRoleType == 1
        ? (this.actions = [{ text: "暂停" }])
        : (this.actions = [{ text: "转交" }]);
    },

    /**
     * 流转信息
     */

    async getTurnover(id) {
      const { data: res } = await GetTurnoverAPI(id);
      this.list2 = res;
    },

    /**
     * 切换
     */
    click(i) {
      this.tabsIndex = i;
      console.log("dianji1");
    },
    // 删除图片
    deletePic(event) {
      this[`fileList`].splice(event.index, 1);
    },

    // 新增图片
    async afterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileListLen = this[`fileList`].length;
      lists.map((item) => {
        this[`fileList`].push({
          ...item,
          status: "uploading",
          message: "上传中",
        });
      });
      for (let i = 0; i < lists.length; i++) {
        try {
          const { data: result } = await upload(lists[i].url);
          console.log(result, "s-------------");

          let item = this[`fileList`][fileListLen];
          this[`fileList`].splice(
            fileListLen,
            1,
            Object.assign(item, {
              status: "success",
              message: "",
              url: result.url,
            })
          );

          fileListLen++;
        } catch (err) {}
      }
    },
    /**
     *更多操作
     */

    handleClick(item) {
      console.log(item, "item");
      if (item.text == "转交") {
        let obj = {
          id: this.id,
          title: "转交",
        };
        uni.navigateTo({
          url: `/pages/components/organization?id=${this.id}`,
        });
      } else {
        console.log("sss");
        this.show = true;
      }
    },
    /**
     * 获取当前时间
     */
    getTime() {
      // 创建一个 Date 对象
      var today = new Date();

      // 获取年、月、日、时、分、秒
      var year = today.getFullYear();
      var month = today.getMonth() + 1; // 月份是从 0 开始计数的，需要加1
      var day = today.getDate();
      var hours = today.getHours();
      var minutes = today.getMinutes();
      var seconds = today.getSeconds();

      // 格式化输出
      var formattedTime =
        year +
        "-" +
        (month < 10 ? "0" : "") +
        month +
        "-" +
        (day < 10 ? "0" : "") +
        day +
        " " +
        (hours < 10 ? "0" : "") +
        hours +
        ":" +
        (minutes < 10 ? "0" : "") +
        minutes +
        ":" +
        (seconds < 10 ? "0" : "") +
        seconds;
      // this.form.recoveryTime = formattedTime;
      // this.form.cancellationTime = formattedTime;

      this.getRoleType == 1
        ? (this.form.cancellationTime = formattedTime)
        : (this.form.recoveryTime = formattedTime);
      console.log(formattedTime, "====");
      // "2023-11-08 19:05:05"
    },
    /**
     * 获取当前时间+3
     */
    getTime3() {
      // 创建一个 Date 对象
      var today2 = new Date();
      // 添加三天（72小时）
      const addDays = (date, days) => {
        let timeToAdd = days * 24 * 60 * 60 * 1000; // 每天毫秒数
        return new Date(date.getTime() + timeToAdd);
      };

      var today = addDays(today2, 3);

      // 获取年、月、日、时、分、秒
      var year = today.getFullYear();
      var month = today.getMonth() + 1; // 月份是从 0 开始计数的，需要加1
      var day = today.getDate();
      var hours = today.getHours();
      var minutes = today.getMinutes();
      var seconds = today.getSeconds();

      // 格式化输出
      var formattedTime =
        year +
        "-" +
        (month < 10 ? "0" : "") +
        month +
        "-" +
        (day < 10 ? "0" : "") +
        day +
        " " +
        (hours < 10 ? "0" : "") +
        hours +
        ":" +
        (minutes < 10 ? "0" : "") +
        minutes +
        ":" +
        (seconds < 10 ? "0" : "") +
        seconds;
      // this.form.recoveryTime = formattedTime;
      // this.form.cancellationTime = formattedTime;

      this.getRoleType == 1
        ? (this.form.rectificationTime = formattedTime)
        : "";
      console.log(formattedTime, "====");
      // "2023-11-08 19:05:05"
    },
    /**
     * 跳转
     * @param {*} val
     */
    toPath(val) {
      console.log(val, "---");
      if (val != "/pages/index/index") {
        console.log("sss");
        uni.navigateBack({
          delta: 1,
        });
      } else {
        uni.switchTab({
          url: "/pages/index/index",
        });
      }
    },

    /**
     * 提交
     */
    async submit(type) {
      console.log(this.fileList, "submidt");

      this.form.imageUrls = JSON.stringify(this.fileList);
      this.form.approvalStatus = type;
      this.form.id = this.id;
      if (type == 0 && !this.form.approvalOpinion) {
        this.$refs.uToast.show({
          type: "error",
          title: "默认主题",
          message: "请填写结果跟踪！",
        });
        return;
      } else if (type == 0 && this.form.approvalOpinion) {
        if (type == 1 && this.form.verifyCancel == 0) {
          this.$refs.uToast.show({
            type: "error",
            title: "默认主题",
            message: "任务未完成，请驳回继续整改！",
          });
          return;
        }
        if (type == 0 && this.form.verifyCancel == 1) {
          this.$refs.uToast.show({
            type: "error",
            title: "默认主题",
            message: "任务完成，请通过！",
          });
          return;
        }
        const result =
          this.getRoleType == 1
            ? await AddSecurityAPI(this.form)
            : await AddSafeAPI(this.form);
        console.log(result, "result.code");
        if (result.code == 200) {
          uni.showToast({
            title: "回复成功",
            duration: 2000,
          });
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
            });
          }, 1000);
          return;
        }
      }

      if (type == 1 && this.form.verifyCancel == 0) {
        this.$refs.uToast.show({
          type: "error",
          title: "默认主题",
          message: "任务未完成，请驳回继续整改！",
        });
        return;
      } else if (type == 0 && this.form.verifyCancel == 1) {
        this.$refs.uToast.show({
          type: "error",
          title: "默认主题",
          message: "任务完成，请通过！",
        });
        return;
      }

      const res = await this.$refs.uForm.validate();
      if (!res) return;

      const result =
        this.getRoleType == 1
          ? await AddSecurityAPI(this.form)
          : await AddSafeAPI(this.form);
      console.log(result, "result.code");
      if (result.code == 200) {
        uni.showToast({
          title: "回复成功",
          duration: 2000,
        });
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
          });
        }, 1000);
      }

      console.log(res, "res");
    },
    rectificationChange(v) {
      if (v == 2) {
        this.form.plannedCompletionTime = "";
      }
      console.log(v);
    },

    verifyChange(v) {
      if (v == 1) {
        this.form.rectificationTime = "";
      } else {
        this.getTime3();
      }
      console.log(v);
    },
    close(e) {
      this.showCll = e.length == 1 ? false : true;
      console.log(this.showCll);
    },

    /**
     * 暂停提交
     */

    async stopSubmit() {
      try {
        const res = await this.$refs.uFormTwo.validate();
        if (!res) return;
        this.formTwo.id = this.id;
        const result = await PauseAPI(this.formTwo);
        this.formTwo.approvalOpinion = "";
        this.show = false;
        if (result.code == 200) {
          uni.showToast({
            title: "暂停成功",
            duration: 2000,
          });
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
            });
          }, 1000);
        }
      } finally {
      }
    },
  },
};
</script>
<template>
  <view class="look">
    <u-navbar
      title="详情"
      bgColor="#5375ff"
      placeholder
      titleStyle="color:#ffffff"
    >
    </u-navbar>
    <view class="tabs">
      <view
        :class="[tabsIndex == i ? 'tabsIndex' : '']"
        v-for="(t, i) in tabs"
        :key="i"
        @click="click(i)"
        >{{ t.name }}</view
      >
    </view>

    <view v-if="tabsIndex == 0" style="height: 65%; overflow-y: auto">
      <u-cell
        :title="'申请信息'"
        :isLink="true"
        :arrow-direction="showCll ? 'down' : 'up'"
        @click="showCll = !showCll"
      ></u-cell>
      <view :class="['record', showCll ? 'is-fold' : 'not-fold']">
        <!-- <view class="main-content"> -->
        <view
          class="content"
          v-for="(item, index) in list"
          :key="index"
          v-show="showCll"
        >
          <view v-if="item.type == '1'">
            <view v-for="(t, i) in keys" :key="i" class="flex">
              <view class="lable">{{ t.label }}:</view>
              <view class="value">
                <view v-if="t.label != '照片上传'">{{
                  item.dataMsg[t.prop]
                }}</view>
                <view v-else>
                  <u-album
                    :urls="item.dataMsg[t.prop]"
                    multipleSize="50"
                    singleSize="50"
                    keyName="url"
                    :rowCount="3"
                  ></u-album>
                </view>
              </view>
            </view>
          </view>
          <view v-if="item.type == '2'" style="margin: 10px 0">
            <view v-for="(t, i) in keys2" :key="i" class="flex">
              <view class="lable">{{ t.label }}:</view>
              <view class="value">
                <view v-if="t.label != '照片上传'">{{
                  item.dataMsg[t.prop]
                }}</view>
                <view v-else>
                  <u-album
                    :urls="item.dataMsg[t.prop]"
                    multipleSize="50"
                    singleSize="50"
                    keyName="url"
                    :rowCount="3"
                  ></u-album>
                </view>
              </view>
            </view>
          </view>
          <view v-if="item.type == '3'" style="margin: 10px 0">
            <view v-for="(t, i) in keys3" :key="i" class="flex">
              <view class="lable">{{ t.label }}:</view>
              <view class="value">
                <view v-if="t.label != '照片上传'">{{
                  item.dataMsg[t.prop]
                }}</view>
                <view v-else>
                  <u-album
                    :urls="item.dataMsg[t.prop]"
                    multipleSize="50"
                    singleSize="50"
                    keyName="url"
                    :rowCount="3"
                  ></u-album>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- </view> -->
      </view>
      <!-- <uni-collapse ref="collapse" @change="close">
        <uni-collapse-item title="申请信息" style="height: 90%"> -->

      <!-- </uni-collapse-item>
      </uni-collapse> -->

      <view v-show="!showCll">
        <view class="form" v-if="status != 4 && status != 3">
          <!-- 当用户是安环员时 -->

          <view v-if="getRoleType == 1">
            <u--form :model="form" :rules="rules" ref="uForm" borderBottom>
              <u-form-item ref="item1" prop="cancellationTime">
                <view style="width: 25%">
                  <text class="identifying">*</text>
                  <text class="disabled">销号时间:</text>
                </view>
                <view style="flex: 1">
                  <uni-datetime-picker
                    type="datetime"
                    :border="false"
                    v-model="form.cancellationTime"
                  />
                </view>
              </u-form-item>
              <u-form-item ref="item1" prop="verifyCancel">
                <view style="width: 25%">
                  <text class="identifying">*</text>
                  <text class="disabled">核查销号:</text>
                </view>
                <view style="flex: 1">
                  <u-radio-group
                    v-model="form.verifyCancel"
                    placement="column"
                    @change="verifyChange"
                    style="display: flex; flex-direction: row"
                  >
                    <u-radio
                      :customStyle="{ marginRight: '8px' }"
                      v-for="(item, index) in radiolist1"
                      :key="index"
                      :label="item.label"
                      :name="item.name"
                      @change="radioChange"
                    >
                    </u-radio>
                  </u-radio-group>
                </view>
              </u-form-item>
              <!-- <u-form-item ref="item1" prop="safetyRecovery">
                <view style="width: 25%">
                  <text class="identifying">*</text>
                  <text class="disabled">安环回复:</text>
                </view>
                <view style="flex: 1">
                  <u--textarea
                    v-model="form.safetyRecovery"
                    placeholder="请输入安环回复"
                    count
                    maxlength="150"
                  ></u--textarea>
                </view>
              </u-form-item> -->
              <u-form-item
                ref="item1"
                prop="rectificationTime"
                v-if="form.verifyCancel == '0'"
              >
                <view style="width: 25%">
                  <text class="identifying">*</text>
                  <text class="identifying">限期整改:</text>
                </view>
                <view style="flex: 1">
                  <uni-datetime-picker
                    type="datetime"
                    :border="false"
                    v-model="form.rectificationTime"
                  />
                </view>
              </u-form-item>
              <u-form-item ref="item1" prop="approvalOpinion">
                <view style="width: 25%">
                  <text class="identifying">*</text>
                  <text class="identifying">结果跟踪:</text>
                </view>
                <view style="flex: 1">
                  <u--textarea
                    v-model="form.approvalOpinion"
                    placeholder="请输入结果跟踪"
                    count
                    maxlength="150"
                  ></u--textarea>
                </view>
              </u-form-item>
            </u--form>
          </view>
          <!-- 当用户是其他角色时 -->

          <view v-else>
            <u--form :model="form" :rules="rules" ref="uForm" borderBottom>
              <u-form-item ref="item1" prop="recoveryTime">
                <view style="width: 25%">
                  <text class="identifying">*</text>
                  <text class="identifying">回复时间:</text>
                </view>
                <view style="flex: 1">
                  <uni-datetime-picker
                    type="datetime"
                    :border="false"
                    v-model="form.recoveryTime"
                  />
                </view>
              </u-form-item>
              <u-form-item ref="item1" prop="rectificationState">
                <view style="width: 25%">
                  <text class="identifying">*</text>
                  <text class="identifying">整改状态:</text>
                </view>
                <view style="flex: 1">
                  <uni-data-select
                    v-model="form.rectificationState"
                    :localdata="rectificationStatesDic"
                    @change="rectificationChange"
                  ></uni-data-select>
                </view>
              </u-form-item>
              <!-- <u-form-item ref="item1" prop="rectificationReply">
                <view style="width: 25%">
                  <text class="identifying">*</text>
                  <text class="identifying">整改回复:</text>
                </view>
                <view style="flex: 1">
                  <u--textarea
                    v-model="form.rectificationReply"
                    placeholder="请输入整改回复"
                    count
                    maxlength="150"
                  ></u--textarea>
                </view>
              </u-form-item> -->
              <u-form-item
                ref="item1"
                prop="plannedCompletionTime"
                v-if="form.rectificationState == 2"
              >
                <view style="width: 25%">
                  <text class="identifying">*</text>
                  <text class="identifying">计划时间:</text>
                </view>
                <view style="flex: 1">
                  <uni-datetime-picker
                    type="datetime"
                    :border="false"
                    v-model="form.plannedCompletionTime"
                  />
                </view>
              </u-form-item>

              <u-form-item ref="item1" prop="approvalOpinion">
                <view style="width: 25%">
                  <text class="identifying">*</text>
                  <text class="identifying">结果跟踪:</text>
                </view>
                <view style="flex: 1">
                  <u--textarea
                    v-model="form.approvalOpinion"
                    placeholder="请输入结果跟踪"
                    count
                    maxlength="150"
                  ></u--textarea>
                </view>
              </u-form-item>

              <u-form-item ref="item1" borderBottom>
                <view style="width: 30%">
                  <text class="disabled">照片回传:</text>
                </view>
                <view style="flex: 1">
                  <view class="p-upload">
                    <u-upload
                      :fileList="fileList"
                      @afterRead="afterRead"
                      @delete="deletePic"
                      :deletable="!deletable"
                      name="file"
                      multiple
                      :maxCount="9"
                    >
                      <p-svg size="80" src="../../../static/SVG/upload.svg" />
                    </u-upload>
                  </view>
                </view>
              </u-form-item>
            </u--form>
          </view>
        </view>
      </view>
    </view>
    <view v-else style="height: 80%">
      <view class="circulation">
        <view class="circulation-content">
          <view class="circulation-content-list" v-for="t in list2" :key="t">
            <view class="drop"> </view>
            <view class="info">
              <view class="titme"> {{ t.createTime }}</view>
              <view class="describe"> {{ t.msg }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="bottom" v-if="tabsIndex == 0 &&!showCll">
      <u-button
        type="primary"
        style="width: 25%; height: 60rpx"
        text="通过"
        @click="submit(1)"
        color="#5ac725"
      ></u-button>
      <u-button
        type="primary"
        style="width: 25%; height: 60rpx"
        color="#e74f4c"
        @click="submit(0)"
        text="驳回"
      ></u-button>
      <zb-popover
        placement="top"
        :options="actions"
        ref="Popover1"
        style="width: 25%; height: 60rpx"
        @select="handleClick"
        class="item-popover"
      >
        <u-button
          type="primary"
          style="height: 60rpx"
          color="#bfbfbf"
          text="更多操作"
        ></u-button>
      </zb-popover>
    </view>

    <view class="bottoms">
      <u-grid :border="false" col="2">
        <u-grid-item
          v-for="(baseListItem, baseListIndex) in baseList"
          :key="baseListIndex"
          @click="toPath(baseListItem.url)"
        >
          <p-svg :src="baseListItem.icon" style="margin-bottom: 10px" />
          <text class="grid-text">{{ baseListItem.title }}</text>
        </u-grid-item>
      </u-grid>
    </view>

    <u-modal
      :show="show"
      title="暂停"
      :showConfirmButton="true"
      :showCancelButton="true"
      @confirm="stopSubmit"
      @cancel="show = false"
    >
      <view style="width: 100%">
        <u--form :model="formTwo" :rules="rules" ref="uFormTwo" borderBottom>
          <u-form-item prop="approvalOpinion" style="width: 100%">
            <view>
              <text class="identifying">*</text>
              <text class="identifying" style="width: 100rpx">结果跟踪:</text>
            </view>
            <view>
              <u--textarea
                v-model="formTwo.approvalOpinion"
                placeholder="请输入结果跟踪"
                count
                style="width: 300rpx"
                maxlength="150"
              ></u--textarea>
            </view>
          </u-form-item>
        </u--form>
        <!-- <rich-text :nodes="content"></rich-text> -->
      </view>
    </u-modal>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<style lang="scss" scoped>
.look {
  background-color: #fff;
  height: 100%;
  // overflow-y: auto;
}

/deep/ .u-icon__icon {
  display: none !important;
}

/deep/ .u-form-item__body {
  padding: 3px 0 !important;
}
.not-fold {
  transition: all 0.2s;
  height: 0px;
}
.is-fold {
  transition: all 0.2s;
  height: 55vh;
  overflow: hidden;
  overflow-y: auto;
  padding: 10rpx;
}
.record {
  border: 1px solid #d1d1d1;
}
// /deep/ .uni-collapse-item__wrap.is--transition {
//   height: 90% !important;
// }

// /deep/ .uni-collapse-item__wrap-content.open {
//   height: 90% !important;
// }
.bottoms {
  position: fixed;
  bottom: 0;
  left: 0;
  height: 150rpx;
  width: 100%;
  border-top: 1px solid #747474;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tabs {
  height: 70rpx;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.bottom {
  display: flex;
  height: 60rpx;
  margin-top: 20rpx;
}

.tabsIndex {
  color: #5375ff;
}
.identifying {
  color: red;
}
.disabled {
  width: 20%;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 30rpx;
  margin-right: 30rpx;
  color: #222222;
}

.flex {
  display: flex;
  margin-bottom: 5rpx;
}
.main-content {
  // height: 90%;
  background-color: #d7d8da;
  border-radius: 20rpx;

  padding: 20rpx;
  // overflow-y: auto;
}
.content {
}
.lable {
  margin-right: 20rpx;
}
.circulation {
  height: 100%;
  background-color: #f6f6f6;
  padding: 20rpx;
  overflow-y: auto;

  .circulation-content {
    border-left: 1px solid #e8e9e9;
    .circulation-content-list {
      display: flex;
    }
    .drop {
      width: 10rpx;
      height: 10rpx;
      background-color: #4aa2fe;
      border-radius: 10rpx;
      margin-top: 20rpx;
      margin-right: 20rpx;
    }
    .info {
      border-radius: 10rpx;
      flex: 1;
      //   height: 400rpx;
      background-color: #fff;
      padding: 20rpx;
      margin-bottom: 20rpx;
      .titme {
        padding: 5rpx 0;
        font-size: 14px;
        color: #8d8d8d;
        border-bottom: 1px solid #dddddd;
      }
      .describe {
        padding: 20rpx;
        font-size: 14px;
      }
    }
  }
}
.form {
  padding: 20rpx;
}
</style>
