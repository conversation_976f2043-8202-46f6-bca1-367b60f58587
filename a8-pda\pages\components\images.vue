<template>
  <view class="p-upload">
    <u-upload
      :fileList="fileList"
      @afterRead="afterRead"
      @delete="deletePic"
      :deletable="!deletable"
      name="file"
      multiple
      :maxCount="deletable ? fileList.length - 1 : 10"
    >
      <p-svg size="80" src="../../static/SVG/upload.svg" />
    </u-upload>
  </view>
</template>

<script>
import { upload } from '@/utils/request'
export default {
  data() {
    return {
      fileList: [],
      deletable: false,
    }
  },

  /**
   * 创建时初始化数据
   */
  onLoad(option) {
    this.deletable = option.deletable
    let urls = null
    if (option.url != 'undefined' && option.url.length > 2) {
      urls = JSON.parse(option.url)
      this.fileList = urls.map((t) => ({
        status: 'success',
        message: '上传完成',
        url: t,
        ...t,
      }))
    }
  },
  /**
   * 离开页面返回数据给父组件
   */
  beforeDestroy() {
    uni.$emit('images', {
      result: JSON.stringify(this.fileList),
      total: this.fileList.length,
    })
  },
  methods: {
    // 删除图片
    deletePic(event) {
      this[`fileList`].splice(event.index, 1)
    },
    // 新增图片
    async afterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file)
      let fileListLen = this[`fileList`].length
      lists.map((item) => {
        this[`fileList`].push({
          ...item,
          status: 'uploading',
          message: '上传中',
        })
      })
      for (let i = 0; i < lists.length; i++) {
        try {
          const { data: result } = await upload(lists[i].url)
          console.log(result)

          let item = this[`fileList`][fileListLen]
          this[`fileList`].splice(
            fileListLen,
            1,
            Object.assign(item, {
              status: 'success',
              message: '',
              url: result.url,
            })
          )

          fileListLen++
        } catch (err) {}
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.p-upload {
  padding: 20px;
}
</style>
