<script>
  import {
    HuanBaoPieAPI,
    AnQuanPieAPI,
    FinishingRateAPI,
  } from "@/api/index/statisticsWorkshop";
  import { TreeDeptSelectAPI } from "@/api/common";

  import heimcharts from "@/components/heimao-echart/heimao-echart";

  import * as echarts from "@/components/static/echarts.min";

  import { mapGetters } from "vuex";
  import { upload } from "@/utils/request";
  export default {
    name: "echarts2",

    computed: {
      ...mapGetters(["getRoleType"]),
    },
    components: {
      heimcharts,
    },
    data() {
      return {
        showTip: false,
        value: 0,
        position: [],
        list1: [
          {
            name: "环保稽查",
          },
          {
            name: "安全检查",
          },
          {
            name: "合格完成率",
          },
        ],
        index: 0,
        params: {
          beginTime: "",
          endTime: "",
        },
        params2: {
          beginTime: "",
          endTime: "",
        },
        params3: {
          beginTime: "",
          endTime: "",
          a: "",
        },
        /**组织架构 */
        dataTree: [],
        range: [],
        range2: [],
        range3: [],

        option: {},
      };
    },
    mounted(option) {
      this.getTime3();
      this.getAudit();
    },
    methods: {
      /**
       * 跳转
       * @param {*} val
       */
      toPath(val) {
        console.log("val", val);
        if (val == 1) {
          uni.navigateBack({
            delta: val,
          });
        } else {
          uni.navigateTo({
            url: "/pages/index/environmental/index",
          });
        }
      },
      /**
       * 组织架构
       */
      async getAudit() {
        const { data: res } = await TreeDeptSelectAPI();
        this.dataTree = res;
        console.log(this.dataTree);
      },


      finishingTimeChange(v) {
        console.log("ss", v);
        this.params2.beginTime = v[0];
        this.params2.endTime = v[1];
        this.init2();
      },
      finishingChange(v) {
        this.init2();
      },


      finishingRate(x, y) {
        return {
          xAxis: {
            type: "category",
            data: x,
          },
          yAxis: {
            type: "value",
          },
          legend: {
            orient: "horizontal",
            bottom: "2%",
          },

          series: [
            {
              name: "合格完成率",
              data: y,
              type: "bar",
              showBackground: true,
              backgroundStyle: {
                color: "rgba(180, 180, 180, 0.2)",
              },
              label: {
                show: true,
                formatter: function (p) {
                  return p.data + "%";
                },
                position: "top",
                color: "inherit",
              },
              // markPoint: {
              //   data: [{ type: "max" }, { type: "min" }],
              //   label: {
              //     show: true,
              //     formatter: function (p) {
              //       console.log(p);
              //       return p.value * 100 + "%";
              //     },
              //   },
              // },、
              colorBy: "data"
              // itemStyle: {
              //   color: function (params) {
              //     // 根据params的
              //     const colorsMap = [
              //       "#80FFA5",
              //       "#00DDFF",
              //       "#37A2FF",
              //       "#FF0087",
              //       "#FFBF00",
              //     ];
              //     return colorsMap[params.dataIndex];
              //   },
              // },
            },
          ],
        };
      },

      /**
       * 合格
       */
      async init2() {

        const { data: res } = await FinishingRateAPI(this.params2);
        console.log(res.ordinate.length > 0);
        if (res.ordinate.length > 0) {
          console.log('t');
          res.ordinate = res.ordinate.map(t => {
            return parseFloat(t) * 100


          })
          console.log(res.ordinate);
        }
        console.log(res.ordinate);

        const option = this.finishingRate(res.abscissa, res.ordinate);
        this.$refs.chart2.init(echarts, (chart) => {
          chart.setOption(this.option);

          // 监听tooltip显示事件
          chart.on("showTip", (params) => {
            this.showTip = true;
            console.log("showTip::");
          });
          chart.on("hideTip", (params) => {
            setTimeout(() => {
              this.showTip = false;
            }, 300);
          });

          this.$nextTick(() => {
            chart.setOption(option);
          });
        });
      },

      click(item) {
        console.log("----", item);
        this.index = item.index;
        if (this.index == 0) {
          this.init();
        }
        if (this.index == 1) {
          this.init3();
        }
        if (item.index == 2) {
          this.init2();
        }
      },
      /**
       * 获取当前时间+3
       */
      getTime3() {
        // 创建一个 Date 对象
        var today = new Date();

        // 获取年、月、日、时、分、秒
        var year = today.getFullYear();
        let arr = ["", ""];
        arr[0] = year + "-" + "01-01";
        arr[1] = year + "-" + "12-31";
        this.range = JSON.parse(JSON.stringify(arr));
        this.range2 = JSON.parse(JSON.stringify(arr));
        this.range3 = JSON.parse(JSON.stringify(arr));

        this.params.beginTime = arr[0];
        this.params.endTime = arr[1];
        this.params3.beginTime = arr[0];
        this.params3.endTime = arr[1];
        this.params2.beginTime = arr[0];
        this.params2.endTime = arr[1];
      },
      save() {
        this.$refs.chart.canvasToTempFilePath({
          success(res) {
            console.log("res::::", res);
          },
        });
      },
    },
  };
</script>
<template>
  <view class="look">
    <view style="height: 75vh; position: relative; z-index: 999999999999">
      <view class="datetime">
        <view class="title">时间选择 :</view>
        <uni-datetime-picker v-model="range2" @change="finishingTimeChange" :clear-icon="false" type="daterange" />
      </view>
      <view class="datetime">
        <view class="title">部门 :</view>
        <uni-data-picker placeholder="请选择部门" popup-title="请选择所在部门" :localdata="dataTree" v-model="params2.deptId"
          :map="{ text: 'label', value: 'id' }" @change="finishingChange">
        </uni-data-picker>
      </view>
      <view style="height: 70vh; position: relative">
        <heimcharts ref="chart2" @finished="init2"></heimcharts>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .look {
    background-color: #fff;
    height: 100%;
    // overflow-y: auto;
  }

  .datetime {
    display: flex;
    justify-content: center;
    padding: 0 30rpx;
    margin-top: 10px;

    .title {
      display: flex;
      align-content: center;
      margin-right: 20rpx;
      width: 22%;
      text-align: right;
    }
  }

  /deep/ .u-tabs__wrapper__nav__item {
    flex: 1;
  }
</style>