<script>
import { GetInitAPI, SubmitSendAPI } from "@/api/index/messageNotification";
import { upload } from "@/utils/request";

export default {
  data() {
    return {
      form: {
        taskNo: "",
      },
      fileList: [],
      deletePic: false,
      rules: {
        notificationType: {
          type: "string",
          required: true,
          message: "通知类型",
          trigger: ["blur", "change"],
        },
        taskSource: {
          type: "string",
          required: true,
          message: "任务来源",
          trigger: ["blur", "change"],
        },
        hiddenDangerType: {
          type: "string",
          required: true,
          message: "隐患类型",
          trigger: ["blur", "change"],
        },
        hiddenDangerDescription: {
          type: "string",
          required: true,
          message: "隐患描述",
          trigger: ["blur", "change"],
        },
        notificationType: {
          type: "string",
          required: true,
          message: "通知类型",
          trigger: ["blur", "change"],
        },
        title: {
          type: "string",
          required: true,
          message: "标题",
          trigger: ["blur", "change"],
        },
        noticeContent: {
          type: "string",
          required: true,
          message: "公告内容",
          trigger: ["blur", "change"],
        },
      },
      hazardType: [],
      hiddenDangerType: [],
      notificationType: [],
      taskSource: [],
    };
  },
  /**初始化 */

  onShow() {
    this.getInit();
  },

  methods: {
    // 删除图片
    deletePic(event) {
      this[`fileList`].splice(event.index, 1);
    },

    // 新增图片
    async afterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileListLen = this[`fileList`].length;
      lists.map((item) => {
        this[`fileList`].push({
          ...item,
          status: "uploading",
          message: "上传中",
        });
      });
      for (let i = 0; i < lists.length; i++) {
        try {
          const { data: result } = await upload(lists[i].url);
          console.log(result);

          let item = this[`fileList`][fileListLen];
          this[`fileList`].splice(
            fileListLen,
            1,
            Object.assign(item, {
              status: "success",
              message: "",
              url: result.url,
            })
          );

          fileListLen++;
        } catch (err) {}
      }
    },
    /**
     * 返回
     */
    toPath(v) {
      if (v == 1) {
        uni.navigateBack({
          delta: v,
        });
      }
    },
    /**
     * 知会
     */
    async change() {
      console.log("==");
      const res = await this.$refs.uForm.validate();
      console.log(res, "ss");
      if (!res) return;
      this.form.imageUrls = JSON.stringify(this.fileList);
      uni.navigateTo({
        url: `/pages/index/messageNotification/Issued?data=${JSON.stringify(
          this.form
        )}`,
      });
      // this.form.imageUrls = JSON.stringify(this.fileList);
      // await SubmitSendAPI(this.form);
      // uni.showToast({
      //   title: "填写成功",
      //   duration: 2000,
      // });
      // setTimeout(() => {
      //   uni.switchTab({
      //     url: "/pages/index/index",
      //   });
      // }, 1000);
    },

    /**初始化信息 */
    async getInit() {
      const { data: res } = await GetInitAPI();

      this.hiddenDangerType = res.hiddenDangerType.map((t) => {
        return {
          value: t.dictValue,
          text: t.dictLabel,
        };
      });
      this.notificationType = res.notificationType.map((t) => {
        return {
          value: t.dictValue,
          text: t.dictLabel,
        };
      });
      this.taskSource = res.taskSource.map((t) => {
        return {
          value: t.dictValue,
          text: t.dictLabel,
        };
      });
      this.form.taskNo = res.taskNo;
    },
  },
};
</script>
<template>
  <view class="pange">
    <u-navbar
      title="消息通知"
      bgColor="#5375ff"
      placeholder
      titleStyle="color:#ffffff"
      height="44px"
      @leftClick="toPath(1)"
    >
    </u-navbar>

    <u--form labelPosition="left" :model="form" :rules="rules" ref="uForm">
      <u-form-item ref="item1" prop="dangerDescription">
        <view style="width: 25%">
          <text class="red">*</text>
          <text class="red">通知单号:</text>
        </view>
        <view class="toaks">{{ form.taskNo }} </view>
      </u-form-item>
      <u-form-item ref="item1" prop="notificationType">
        <view style="width: 25%">
          <text class="red">*</text>
          <text class="red">通知类型:</text>
        </view>
        <view style="flex: 1">
          <uni-data-select
            v-model="form.notificationType"
            :localdata="notificationType"
          ></uni-data-select>
        </view>
      </u-form-item>

      <!-- 任务通知 -->
      <view v-if="form.notificationType == 2">
        <u-form-item ref="item1" prop="taskSource">
          <view style="width: 25%">
            <text class="red">*</text>
            <text class="red">任务来源:</text>
          </view>
          <view style="flex: 1">
            <uni-data-select
              v-model="form.taskSource"
              :localdata="taskSource"
            ></uni-data-select>
          </view>
        </u-form-item>
        <u-form-item ref="item1" prop="hiddenDangerType">
          <view style="width: 25%">
            <text class="red">*</text>
            <text class="red">隐患类型:</text>
          </view>
          <view style="flex: 1">
            <uni-data-select
              v-model="form.hiddenDangerType"
              :localdata="hiddenDangerType"
            ></uni-data-select>
          </view>
        </u-form-item>
        <u-form-item ref="item1" prop="hiddenDangerDescription">
          <view style="width: 25%">
            <text class="red">*</text>
            <text class="red">隐患描述:</text>
          </view>
          <view style="flex: 1">
            <u--textarea
              v-model="form.hiddenDangerDescription"
              placeholder="请输入隐患描述"
              count
            ></u--textarea>
          </view>
        </u-form-item>
        <u-form-item ref="item1" prop="remark">
          <view style="width: 25%">
            <text>备注:</text>
          </view>
          <view style="flex: 1">
            <u--textarea
              v-model="form.remark"
              placeholder="请输入备注"
              count
            ></u--textarea>
          </view>
        </u-form-item>
      </view>
      <view v-if="form.notificationType == 1">
        <u-form-item ref="item1" prop="title">
          <view style="width: 25%">
            <text class="red">*</text>
            <text class="red">标题:</text>
          </view>
          <view style="flex: 1">
            <u--textarea
              :maxlength="60"
              v-model="form.title"
              placeholder="请输入标题"
              count
            ></u--textarea>
          </view>
        </u-form-item>
        <u-form-item ref="item1" prop="noticeContent">
          <view style="width: 25%">
            <text class="red">*</text>
            <text class="red">公告内容:</text>
          </view>
          <view style="flex: 1">
            <u--textarea
              :maxlength="500"
              v-model="form.noticeContent"
              placeholder="请输入公告内容"
              count
            ></u--textarea>
          </view>
        </u-form-item>
        <u-form-item ref="item1" borderBottom>
          <view style="width: 30%"
            ><text class="disabled">上传图片: </text></view
          >
          <view style="flex: 1">
            <view class="p-upload">
              <u-upload
                :fileList="fileList"
                @afterRead="afterRead"
                @delete="deletePic"
                :deletable="!deletable"
                name="file"
                multiple
                :maxCount="9"
              >
                <p-svg size="60" src="../../../static/SVG/upload.svg" />
              </u-upload>
            </view>
          </view>
        </u-form-item>
      </view>
      <!-- <u-form-item ref="item1" prop="dangerDescription">
        <view style="width: 25%">
          <text class="red">*</text>
          <text class="red">知会:</text>
        </view>
        <view style="flex: 1">
          <u-button
            type="primary"
            text="选择知会人员"
            @click="change"
          ></u-button>
        </view>
      </u-form-item> -->
      <u-button
        class="bot"
        type="primary"
        text="选择知会人员"
        @click="change"
      ></u-button>
    </u--form>
  </view>
</template>

<style lang="scss" scoped>
.pange {
  height: 100vh;
  background-color: #fff;
  padding: 10rpx;
  position: relative;
}

/deep/ .u-icon__icon {
  color: #fff !important;
}

.red {
  color: red;
}

.top {
  display: flex;
}

.toaks {
  flex: 1;
  padding: 10rpx;
  background-color: #e0e0e0;
}

.bot {
  position: absolute;
  bottom: 5%;
  left: 50%;
  width: 80%;
  transform: translateX(-50%);
}
</style>