<script>
export default {
  name: "pCard",
  props: {
    title: {
      type: String,
      default: "",
    },
    /**
     * 标题是否需要下边框
     */
    border: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否所有内容都要下边框
     */
    allBorder: {
      type: Boolean,
      default: false,
    },
    keys: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    /**
     * 是否需要padding
     */
    padding: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dataArr: [],
    };
  },

  /**
   * 根据转入的keys和对象数据自动匹配
   */
  computed: {
    autoMatch() {
      if (JSON.stringify(this.data) === "{}") {
        return [];
      }
      this.dataArr = this.keys.map((t) => ({
        label: t.label,
        value:
          this.data[t.prop] == null
            ? "--"
            : this.data[t.prop]
            ? this.data[t.prop]
            : "未匹配到数据传入字段为=>" + t.prop,
      }));
      return this.dataArr;
    },
  },
};
</script>
<template>
  <view class="card" :style="{ padding: padding ? '10px' : '0' }">
    <slot name="header" class="header title">
      <view class="header title"> {{ title }}</view>
    </slot>
    <view
      v-if="border"
      style="border-bottom: 0.5px solid #f0f0f0; margin-top: 5px"
    ></view>
    <slot>
      <view class="content" v-if="keys.length > 0">
        <view
          class="content-item"
          :style="{ borderBottom: allBorder ? '0.5px solid #f0f0f0' : '' }"
          v-for="(t, i) in autoMatch"
          :key="i"
        >
          <view class="label" :style="{ width: t.label.length * 1.2 + 'em' }"
            >{{ t.label }}:</view
          >
          <view class="value">
            <u-read-more
              textIndent="0"
              showHeight="50"
              closeText="点击查看更多信息"
            >
              <rich-text :nodes="t.value"></rich-text>
            </u-read-more>
          </view>
        </view>
      </view>
    </slot>
  </view>
</template>

<style lang="scss" scoped>
.card {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 10px;
  margin: 0 10px;
  .header {
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .content {
    display: flex;
    flex-wrap: wrap;
    .content-item {
      width: 100%;
      display: flex;
      margin: 10px 0;
      .label {
        color: #747474;
      }
      .value {
        flex: 1;

        color: #000;
      }
    }
  }
}

uni-rich-text {
  color: #000;
}
</style>
