<script>
import { GetTeamMemberAPI } from "@/api/common";
export default {
  props: ["teamObj"],

  data() {
    return {
      teamsList: [], //班组下拉
      teamsFrom: {
        teamId: "",
        teamUser: "",
        teamUserIds: [],
      }, //班组信息
      teams: [],
      dataObj: {},
      checkboxList: [],
      rules: {
        teamId: {
          type: "string",
          required: true,
          message: "请选择班组",
          trigger: ["blur", "change"],
        },
        teamUserIds: {
          type: "array",
          required: true,
          message: "请选择派工人",
          trigger: ["blur", "change"],
        },
      },
    };
  },

  methods: {
    /**
     * 处理数据
     */
    getStr(v) {
      let str = this.checkboxList
        .filter((item2) => {
          return v.some((item1) => item1 === item2.id);
        })
        .map((t) => t.name)
        ?.join(",");
      this.teams = this.checkboxList.filter((item2) => {
        return v.some((item1) => item1 === item2.id);
      });
      console.log(this.teams, "this.teams");

      this.$set(this.teamsFrom, "teamUser", str);
    },
    /**
     * 班组选择
     */
    async onChange(v) {
      if (!v) {
        this.teamsFrom.teamUserIds = [];
        this.checkboxList = [];
        this.teamsFrom.teamUser = "";
        return;
      }
      if (v) {
        const val = this.teamsList.filter((t) => t.value == v)[0];
        Object.keys(val).map((t) => (this.teamsFrom[t] = val[t]));
        const { data: res } = await GetTeamMemberAPI(v);
        this.checkboxList = res;
        this.teamsFrom.teamUserIds = this.checkboxList.map((t) => t.id);

        // /**
        //  * 处理数据
        //  */
        this.getStr(this.teamsFrom.teamUserIds);
      }
    },

    /**
     * 选择人员
     */
    checkboxChange(v) {
      console.log("v", v);
      this.getStr(v);
    },

    /**
     * 提交
     */
    async subMit() {
      const res = await this.$refs.uForm.validate();
      console.log(res, "res");
      if (!res) return false;
      return this.teamsFrom;
    },
  },
  /**
   * 获取班组下拉
   */
  async mounted() {
    console.log(this.teamObj);

    /**
     * 回显
     */
  },
};
</script>
<template>
  <view class="p-teams">
    <u--form labelPosition="left" :model="teamsFrom" :rules="rules" ref="uForm">
      <u-form-item prop="teamId" borderBottom>
        <view class="select">
          <uni-data-select
            clear
            placeholder="请选择班组"
            v-model="teamsFrom.teamId"
            :localdata="teamsList"
            @change="onChange"
          />
        </view>
      </u-form-item>
      <u-form-item>
        <!-- <u--input
          style="margin-top: 20px"
          placeholder="请选择派工人"
          border="surround"
          v-model="teamsFrom.teamUser"
          @change="change"
        ></u--input> -->
        <view class="teamUser">
          <view class="teamUser_box" v-for="t in teams" :key="t.id">{{
            t.name
          }}</view>
        </view>
      </u-form-item>
      <u-form-item prop="teamUserIds" borderBottom>
        <u-checkbox-group
          style="margin-top: 20px"
          v-model="teamsFrom.teamUserIds"
          placement="column"
          @change="checkboxChange"
        >
          <u-checkbox
            :customStyle="{ marginBottom: '8px' }"
            v-for="(item, index) in checkboxList"
            :key="index"
            :label="item.name"
            :name="item.id"
            :value="item.value"
          >
          </u-checkbox>
        </u-checkbox-group>
      </u-form-item>
    </u--form>
  </view>
</template>

<style lang="scss" scoped>
/deep/ .uni-select {
  border: none !important;
}

.p-teams {
  width: 100%;
  height: 50vh;
}

.select {
  // border: none !important;
  border: 1px solid #dadada98 !important;
  border-radius: 5px;
  width: 100%;
}

.teamUser {
  border: 1px solid #dadada98;
  border-radius: 5px;
  height: 50px;
  min-height: 30px;
  // min-width: 280px;
  width: 100%;
  padding: 5px;
  padding: 5px;
  margin-top: 10px;
  border-radius: 5px;
  display: flex;
  flex-wrap: wrap;

  .teamUser_box {
    padding: 0 5px;
    margin: 0 5px;
    height: 25px;
    line-height: 25px;
    margin-bottom: 5px;
    border-radius: 5px;
    background-color: #e9f5fd;
    color: #239ceb;
  }
}
</style>