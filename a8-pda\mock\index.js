// mock文件夹下的test.js内容
import "./mock.browser.js";
import Mock from "./mock.mp.js";
import config from "@/utils/baseUrl";

Mock.mock(config.baseUrl + "/mock/test", {
  // 属性 list 的值是一个数组，其中含有3个元素
  "data|10": [
    {
      "status|1": ["未恢复", "待维保", "待派工"],
      "state|1": ["一般", "紧急"],
      "title|1": ["冲压机", "清洗机", "机械臂"],
      "content|1": ["我是假的数据", "我是真的数据"],
    },
  ],
});

Mock.mock(config.baseUrl + "/mock/message", {
  // 属性 list 的值是一个数组，其中含有3个元素
  "rows|10": [
    {
      "createTime|1": ["2024 09 12:00", "2024 09 12:00", "2024 09 12:00"],

      "content|1": ["我是假的数据", "我是真的数据"],
    },
  ],
  total: 10,
});

Mock.mock(config.baseUrl + "/pureut-app/alarmLog/list1", {
  // 属性 list 的值是一个数组，其中含有3个元素
  "rows|10": [
    {
      "createTime|1": ["2024 09 12:00", "2024 09 12:00", "2024 09 12:00"],
      " statusDict|1": ["未确认", "已报修", "已恢复"],
      "content|1": ["我是假的数据", "我是真的数据"],
    },
  ],
  total: 10,
});
