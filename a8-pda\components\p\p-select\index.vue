<template>
  <view class="p-select">
    <uni-data-select
      clear
      :placeholder="placeholder"
      v-model="selectedValue"
      :localdata="dataSel"
      @change="onChange"
    />
  </view>
</template>

<script>
export default {
  props: {
    /**
     * 绑定数据
     */
    modelValue: {
      type: [String, Number],
      default: () => "",
    },
    /**
     * 下拉选项
     */
    options: {
      type: Array,
      default: () => [],
    },
    /**
     * 唯一标识
     */
    keyId: {
      type: String,
      default: "value",
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    /**
     * 显示文字
     */
    label: {
      type: String,
      default: "label",
    },
  },
  data() {
    return {
      selectedValue: "",
      list: [],
    };
  },
  /**
   * 改变数据
   */
  onShow() {},
  onLoad() {},
  methods: {
    onChange(e) {
      this.$emit("update:modelValue", e);
      const find = this.list.find((t) => t[this.keyId] == e);
      console.log(find);
      if (!find) return;
      this.$emit("change", { value: e, obj: find });
    },
  },
  watch: {
    modelValue: {
      handler(newVal, loaVal) {
        console.log(newVal, loaVal);
        this.selectedValue = newVal;
      },
    },
    immediate: true, // 组件创建时立即触发
    deep: true, // 对象内部属性变化时也触发
  },
  computed: {
    dataSel() {
      this.list = this.options.map((t) => ({
        text: t[this.label],
        value: t[this.keyId],
      }));
      return this.list;
    },
  },
};
</script>

<style scoped></style>
