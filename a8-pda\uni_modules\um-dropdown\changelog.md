## 1.1.0（2025-05-12）
优化文档和部分代码
## 1.0.9（2024-11-05）
新增：ref实例close事件，可用于主动控制下拉框收缩
## 1.0.8（2023-11-23）
*修复：异步加载数据时，默认下标为0时无法选中
## 1.0.7（2023-10-09）
*修复：异步加载数据时，默认下标为0时无法选中
## 1.0.6（2023-08-29）
*新增-H5环境下点击列表外部区域，列表自动收起（小程序目前没有实现该功能）
*优化-小程序环境下列表在页面加载时会闪动
## 1.0.5（2023-08-29）
*新增-发生点击事件时，目标不是下拉菜单会自动收起，当界面存在多个下拉菜单时，点击其中一个其余的下拉菜单会收起
## 1.0.4（2023-04-06）
优化异步数据下设置默认值报错的情况
## 1.0.3（2023-03-21）
代码优化，新增自定义列表和菜单样式
## 1.0.2（2023-03-20）
新增自定义列表和菜单样式
## 1.0.1（2023-02-12）
优化缩小组件体积
## 1.0.0（2023-02-12）
1.0.0
