{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.quality.domain.TaskWgjy", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询任务表头列表\n"}, {"name": "add", "paramTypes": ["org.dromara.quality.domain.TaskWgjy"], "doc": " 新增任务表头\n"}, {"name": "edit", "paramTypes": ["org.dromara.quality.domain.TaskWgjy"], "doc": " 新增任务表头\n"}, {"name": "updateTaskWgjyItem", "paramTypes": ["org.dromara.quality.domain.TaskWgjyItem"], "doc": " 修改任务明细\n"}, {"name": "updateAppearanceStatus", "paramTypes": ["org.dromara.quality.domain.TaskWgjyItem"], "doc": " 外观判定后修改任务项目\n"}, {"name": "getTaskWgjyItem", "paramTypes": ["org.dromara.quality.domain.TaskWgjyItem"], "doc": " 获取程序验证任务明细\n"}, {"name": "generateTaskWgjyItem", "paramTypes": ["org.dromara.quality.domain.TaskWgjy"], "doc": " 生成程序验证任务明细\n"}, {"name": "getOutsideBadness", "paramTypes": ["org.dromara.quality.domain.TaskWgjyBad"], "doc": " 获取外观不良列表\n"}, {"name": "tableSelectSampleByMaterielCode", "paramTypes": ["org.dromara.quality.domain.vo.TableSelectSampleByMaterielCodeVo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据物料编码查询可选抽样方案\n"}, {"name": "addTaskWgjyItem", "paramTypes": ["org.dromara.quality.domain.TaskWgjyItem"], "doc": " 新增检验任务明细的检验项\n"}, {"name": "addOutsideBadness", "paramTypes": ["org.springframework.web.multipart.MultipartFile[]", "org.dromara.quality.domain.TaskWgjyBad"], "doc": " 检验员新增外观不良\n"}, {"name": "getOutsideBadnessAttachmentList", "paramTypes": ["org.dromara.quality.domain.TaskWgjyBad"], "doc": " 获取外观不良附件列表 检验员\n"}, {"name": "deleteOutsideBadness", "paramTypes": ["java.lang.Long"], "doc": " 删除外观不良\n"}, {"name": "deleteTaskWgjyItem", "paramTypes": ["java.lang.Long"], "doc": " 删除程序验证任务明细\n"}, {"name": "updateTaskStatus", "paramTypes": ["org.dromara.quality.domain.TaskWgjy"], "doc": " 修改程序任务的状态\n"}, {"name": "addOutsideBadnessReview", "paramTypes": ["org.springframework.web.multipart.MultipartFile[]", "org.dromara.quality.domain.TaskWgjyBadReview"], "doc": " 增加外观不良 主管\n"}, {"name": "getOutsideBadnessReview", "paramTypes": ["org.dromara.quality.domain.TaskWgjyBadReview"], "doc": " 获取外观不良列表 主管\n"}, {"name": "updateOutsideBadnessReview", "paramTypes": ["org.dromara.quality.domain.TaskWgjyBadReview"], "doc": " 修改外观不良 主管\n"}, {"name": "deleteOutsideBadnessReview", "paramTypes": ["java.lang.Long"], "doc": " 删除外观不良 主管\n"}, {"name": "getBadFileListReview", "paramTypes": ["org.dromara.quality.domain.TaskWgjyBadReview"], "doc": " 获取外观不良附件列表 主管\n"}, {"name": "getTaskItemWgjyAll", "paramTypes": ["org.dromara.quality.domain.vo.TaskItemAllVo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 检验任务明细\n"}, {"name": "exportTaskItemWgjyAll", "paramTypes": ["org.dromara.quality.domain.vo.TaskItemAllVo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出检验任务明细\n"}, {"name": "tableSelectOnProcessName", "paramTypes": ["org.dromara.quality.domain.vo.TableSelectOnProcessNameVo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 工序名称表格选择带回\n"}, {"name": "generateReinspection", "paramTypes": ["java.lang.String"], "doc": " 生成复检单\n"}, {"name": "getReinspectionStatus", "paramTypes": ["java.lang.String"], "doc": " 获取单据的可复检状态\n"}, {"name": "getTaskWgjyItemIsStart", "paramTypes": ["org.dromara.quality.domain.TaskWgjyItem"], "doc": " 获取任务明细是否已经开始\n"}], "constructors": []}