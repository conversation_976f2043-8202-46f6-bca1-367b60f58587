import request from "@/utils/request"
/**
 * 任务
 * @returns {object} params 查询条件
 */
export const GetListAPI = (params) => {
    return request({
        url: "/wms/receiptorder/listAPP",
        method: "GET",
        params,
    })
}

/**sap列表 */
export const GetListSapAPI = (params) => {
    return request({
        url: "/wms/receiptorder/sapList",
        method: "GET",
        params,
    })
}
/**
 *
 * 获取详情
 * @returns {object} data
 */
export const GetViewAPI = (data) => {
    return request({
        url: "/wms/receiptorder/" + data,
        method: "GET",
    })
}

/**
 *
 * 获取详情sAP
 * @returns {object} data
 */
export const GetViewAPISap = (data) => {
    return request({
        url: "/wms/receiptorder/sap/" + data,
        method: "GET",
    })
}

/**获取明细列表 */
export const ReceiptorderItemAPI = (params) => {
    return request({
        url: "/wms/receiptorderItem/list",
        method: "GET",
        params
    })
}
/**
 *
 * 获取物料详情
 * @returns {object} data
 */
export const ReceiptorderAPI = (data) => {
    return request({
        url: "/wms/receiptorderItem/" + data,
        method: "GET",
    })
}


/**产品信息 */

export const CoderulesproinfoAPI = (params) => {
    return request({
        url: "/wms/coderulesproinfo/list",
        method: "GET",
        params,
    })
}

/**追溯码解析 */

export const BarcodeParsingAPI = (params) => {
    return request({
        url: "/wms/receiptorder/barcodeParsing",
        method: "GET",
        params,
    })
}

/**确认配送 */
export const DeliveryAPI = (data) => {
    return request({
        url: "/wms/receiptorderItem/delivery",
        method: "PUT",
        data,
    })
}

/**历史扫码 */
export const scanRecordAPI = (params) => {
    return request({
        url: "/wms/scanRecord/list",
        method: "GET",
        params,
    })
}


/**物料下拉
 * 
 */

export const getInfoMaterieAPI = (data) => {
    return request({
        url: "/wms/receiptorder/materiel/" + data,
        method: "GET",

    })
}

/**撤销确认 */
export const RevokeAPI = (data) => {
    return request({
        url: "/wms/receiptorderItem/revoke",
        method: "PUT",
        data,
    })
}

/**获取总和 */
export const TraceableAPI = (params) => {
    return request({
        url: "/wms/scanRecord/traceable",
        method: "GET",
        params
    })
}
/**获取历史扫码总和 */
export const deliverySumAPI = (params) => {
    return request({
        url: "/wms/scanRecord/delivery",
        method: "GET",
        params
    })
}

/**sap 配送数量 */
export const GetsendInfoAPI = (params) => {
    return request({
        url: "/wms/receiptorderItem/sendInfo",
        method: "GET",
        params,
    })
}