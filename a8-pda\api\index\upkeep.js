import request from "@/utils/request"

/**
 * 保养任务目录
 */
export const getExecutionList = (current, size, params) => {
    return request({
        url: '/eap/upkeepTask/getExecutionList',
        method: 'GET',
        params: {
            pageNum: current,
            pageSize: size,
			...params
        }
    })
}

/**
 * 执行目录
 */
export const getExecutionItem = (current, size, params) => {
    return request({
        url: '/eap/upkeepTask/getExecutionItem',
        method: 'GET',
        params: {
            pageNum: current,
            pageSize: size,
			...params,
        }
    })
}

export const update = (row) => {
    return request({
        url: '/eap/upkeepTask/submit',
        method: 'POST',
        data: row
    })
}

export const changeExecutionToConfirm = (ids) => {
    return request({
        url: '/eap/upkeepTask/changeExecutionToConfirm',
        method: 'GET',
        params: {
            ids
        }
    })
}

/**
 * 执行确认
 */
export const getConfirmList = (current, size, params) => {
    return request({
        url: '/eap/upkeepTask/getConfirmList',
        method: 'GET',
        params: {
            pageNum: current,
            pageSize: size,
			...params,
        }
    })
}

export const getConfirmItem = (current, size, params) => {
    return request({
        url: '/eap/upkeepTask/getConfirmItem',
        method: 'GET',
        params: {
            pageNum: current,
            pageSize: size,
			...params,
        }
    })
}

export const changeConfirmToFinish = (ids) => {
    return request({
        url: '/eap/upkeepTask/changeConfirmToFinish',
        method: 'GET',
        params: {
            ids
        }
    })
}
/**
 * 一键执行
 */
export const appConfirmOneClickExecution = (queryData) => {
    return request({
        url: '/eap/upkeepTask/appConfirmOneClickExecution',
        method: 'POST',
        data: queryData
    })
}

export const selectDeviceTypeno = () => {
    return request({
        url: '/eap/deviceType/selectDeviceTypeno',
        method: 'GET'
    })
}

export function TreeTwoAPI(params) {
    return request({
        url: '/prod/factoryModeling/treeTwo',
        method: 'GET',
        params: {
            ...params
        }
    });
}

export function TreeThreeAPI(params) {
    return request({
        url: '/prod/factoryModeling/treeThree',
        method: 'GET',
        params: {
            ...params
        }
    });
}
