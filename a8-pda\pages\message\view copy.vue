<script>
import { GetViewAPI } from "@/api/index/message";
import view from "../index/selfExamination/view.vue";

export default {
  components: { view },
  data() {
    return {
      form: {},
      noticeKey: [
        { name: "通知单号", key: "taskNo" },
        { name: "通知类型", key: "notificationTypeDic" },

        { name: "标题", key: "title" },
        { name: "公告内容", key: "noticeContent" },
      ],
      taskKey: [
        { name: "通知单号", key: "taskNo" },
        { name: "通知类型", key: "notificationTypeDic" },
        { name: "任务来源", key: "taskSource" },
        { name: "隐患类型", key: "hiddenDangerType" },
        { name: "隐患描述", key: "hiddenDangerDescription" },
        { name: "备注", key: "remark" },
      ],
    };
  },

  onLoad(o) {
    this.getView(o.id);
  },

  methods: {
    /**
     * 详情
     */
    async getView(id) {
      const { data: res } = await GetViewAPI(id);
      this.form = res;
    },
    /**
     * 跳转
     */
    toPath(v) {
      if (v == 1) {
        uni.navigateBack({
          delta: v,
        });
      }
    },
  },
};
</script>
<template>
  <view class="view">
    <u-navbar
      title="消息通知详情"
      bgColor="#5375ff"
      placeholder
      titleStyle="color:#ffffff"
      height="44px"
      @leftClick="toPath(1)"
    >
    </u-navbar>

    <view v-if="form.notificationType == 1" class="box">
      <view class="list" v-for="(item, index) in noticeKey" :key="index">
        <view class="lable">{{ item.name }}:</view>
        <view class="value">{{ form[item.key] }}</view>
      </view>
      <view class="list" v-if="JSON.parse(form.imageUrls).length != 0">
        <view class="lable">照片：</view>
        <view class="value" v-if="JSON.parse(form.imageUrls).length != 0">
          <u-album
            :urls="JSON.parse(form.imageUrls).map((t) => t.url)"
          ></u-album>
        </view>
      </view>
    </view>

    <view v-else class="box">
      <view class="list" v-for="(item, index) in taskKey" :key="index">
        <view class="lable">{{ item.name }}:</view>
        <view class="value">{{ form[item.key] }}</view>
      </view>
    </view>

    <view class="lll">
      <u--image
        :showLoading="true"
        src="../../static/images/lll.png"
        width="30px"
        height="30px"
        @click="click"
      ></u--image>
      <view class="num">共{{ form.countNum }} 未读{{ form.unreadNum }}人</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.view {
  height: 100vh;
  background-color: #fff;
  padding: 20rpx;
}
.box {
  height: 70vh;
  border-top: 1px solid rgb(196, 194, 194);
  border-bottom: 1px solid rgb(196, 194, 194);
  padding: 30rpx 0rpx;
  overflow: hidden;
  overflow-y: auto;
}
.list {
  display: flex;
  margin-bottom: 10rpx;
  .lable {
    min-width: 140rpx;
    text-align: right;
    margin-right: 30rpx;
  }
}
/deep/ .u-icon__icon {
  color: #fff !important;
}
.lll {
  padding: 20rpx;
  display: flex;
  .num {
    font-size: 14px;
    color: rgb(163, 163, 163);
    margin-left: 10px;
  }
}
</style>