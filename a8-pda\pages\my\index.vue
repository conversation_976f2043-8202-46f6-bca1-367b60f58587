<template>
  <view class="my-bg">
    <u-navbar
      title="我的"
      bgColor="background-color:rgba(0,0,0,0)"
      placeholder
      titleStyle="color:#ffffff"
    >
    </u-navbar>

    <view class="my-top">
      <view class="image">
        <u--image
          width="80px"
          height="80px"
          :src="getInfo.avatar || 'https://cdn.uviewui.com/uview/album/1.jpg'"
          shape="circle"
        ></u--image>
      </view>
      <view class="my-info">
        <view class="name">{{ getInfo.nickName }}</view>
        <view class="bumen">{{ getInfo.deptName }}</view>
      </view>
    </view>

    <view class="my-bom">
      <u-cell-group :border="false">
        <u-cell
          v-for="(item, index) in list"
          :key="index"
          :border="false"
          @click="click(item)"
        >
          <view slot="title" class="u-slot-title flex">
            <p-svg :src="item.icon" style="margin-right: 20rpx" />
            <text class="u-cell-text">{{ item.text }}</text>
          </view>
          <view slot="value">
            <uni-icons
              v-if="item.text != '版本' && item.text != '版本更新'"
              type="right"
              size="20"
              color="#cccccc"
            >
            </uni-icons>

            <view v-if="item.text == '版本'" class="version">
              <view class="text">{{ appversion }}</view>
              <view class="red" v-if="appversionNew != appversion"></view>
            </view>

            <view
              v-if="item.text == '版本更新'"
              class="version"
              @click="appversionNewChange(appversionNew != appversion)"
            >
              <p-svg :src="item.rightIcon" style="margin-right: 20rpx" />
            </view>
          </view>
        </u-cell>
      </u-cell-group>
    </view>
    <p-update
      :versio="appversionNew"
      v-if="appversionShow"
      :show="appversionShow"
      :appURL="appURL"
      @change="
        () => {
          appversionShow = false;
        }
      "
    ></p-update>
    <u-toast ref="uToast"></u-toast>
    <!-- <u-notify ref="uNotify"></u-notify> -->
  </view>
</template>

<script>
import { mapGetters } from "vuex";
import { GetversionAPI } from "@/api/common.js";
// #ifdef APP-PLUS
import navigation from "../components/navigationBar.vue";

let pages = getCurrentPages();
let page = pages[pages.length - 1];
let currentWebview = page.$getAppWebview();
// #endif
export default {
  computed: {
    ...mapGetters(["getInfo"]),
  },

  components: { navigation },
  data() {
    return {
      src: "https://cdn.uviewui.com/uview/album/1.jpg",
      show: false,
      appversion: "",
      appversionNew: "",
      appversionShow: false,
      appURL: "",
      list: [
        {
          icon: "../../static/images/xiugai.png",
          text: "修改资料",
          color: "#000000",
          size: "60px",
          path: "/pages/index/inspect/index",
        },
        {
          icon: "../../static/images/mima.png",
          text: "修改密码",
          color: "#f0902d",
          size: "80px",
          path: "/pages/changePassword/index",
        },
        {
          icon: "../../static/images/banben.png",
          text: "版本",
          color: "#f0902d",
          size: "80px",
          path: "2",
        },
        {
          icon: "../../static/images/home/<USER>",
          text: "版本更新",
          color: "#f0902d",
          size: "80px",
          rightIcon: "../../static/images/home/<USER>",
          path: "2",
        },
        {
          icon: "../../static/images/tuichu.png",
          text: "退出登陆",
          color: "#f0902d",
          size: "80px",
          path: "1",
        },
      ],
    };
  },
  async onShow() {
    const res = await this.getAPP();
    this.appversion = res.version;
    this.getAppversion();
  },

  async onload() {},

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      this.$store.dispatch("getUseInfo");
    } catch (err) {
    } finally {
      uni.stopPullDownRefresh();
    }
  },
  methods: {
    /*版本号*/
    async getAppversion() {
      /**获取最新版本号 */
      let { data: res } = await GetversionAPI();
      this.appversionNew = res.version;
      this.appURL = res.address;
      console.log(
        this.appversionNew,
        this.appversion,
        this.appversionNew == this.appversion
      );
    },

    getAPP() {
      /* #ifdef APP-PLUS */
      return new Promise((resolve, reject) => {
        plus.runtime.getProperty(
          plus.runtime.appid,
          (wgtinfo) => {
            resolve(wgtinfo);
          },
          (error) => {
            reject(error);
          }
        );
      });
      /* #endif */
    },

    /*更新 */
    async appversionNewChange(bor) {
      // #ifdef APP-PLUS
      //判断是否是最新版本 true 不是最新版本，false，是最新版本
      if (bor) {
        this.appversionShow = true;
        console.log(this.appversionShow, " this.appversionShow");
      } else {
        this.$refs.uToast.show({
          type: "success",
          title: "成功主题(带图标)",
          message: "当前已经最新包",
        });
      }
      // #endif
    },

    toPath(url) {
      uni.navigateTo({
        url,
      });
    },

    /**
     * 点击
     */
    click(v) {
      if (v.text == "退出登陆") {
        this.logOut();
      }
      if (["修改密码"].includes(v.text)) {
        uni.navigateTo({
          url: v.path,
        });
      }
    },
    /**
     * 退出登录
     */
    async logOut() {
      let _this = this;
      uni.showModal({
        title: "提示",
        content: "确定退出登录",
        success: function (res) {
          if (res.confirm) {
            _this.$store.dispatch("logOut");
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
  },
  onNavigationBarButtonTap: function (e) {
    uni.navigateTo({
      url: "/pages/index/message/index",
    });
  },
};
</script>

<style lang="scss">
/deep/ .u-navbar__content__title {
  font-weight: 600;
}

.my-bg {
  box-sizing: border-box;
  align-items: center;
  background-image: url("/static/images/home-bg2.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  height: 100%;
  box-sizing: border-box;

  .my-top {
    height: 20%;
    display: flex;
    padding-left: 50rpx;
    align-items: center;
  }

  .my-bom {
    height: 80%;
    width: 100%;
    background: #ffffff;
    padding: 20rpx;
    box-sizing: border-box;
    border-radius: 30rpx;
  }

  .my-info {
    color: #fff;
    height: 40%;
    margin-left: 30rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;

    .name {
      font-family: Adobe Heiti Std;
      font-weight: normal;
      font-size: 36rpx;
      color: #ffffff;
    }

    .bumen {
      font-family: PingFang;
      font-weight: 500;
      font-size: 28rpx;
      color: #ffffff;
      opacity: 0.5;
    }
  }
}

.flex {
  display: flex;
  align-items: center;
}

.version {
  position: relative;

  .text {
    color: #9b9999;
  }

  .red {
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: red;
    position: absolute;
    right: -30%;
    top: 0;
  }
}

/deep/ .u-icon__icon {
  display: none !important;
}

/deep/ .u-navbar__content {
  background-color: rgba(0, 0, 0, 0) !important;
}
</style>