<template>
  <view>
    <view>
      <u-navbar
        :title="navbarTitle"
        @leftClick="toPath()"
        bgColor="#4669ea"
        placeholder
      ></u-navbar>
      <uni-section
        v-if="currentTabIndex !== 2"
        :title="'出库单号:' + item.deliveryNo"
        type="line"
        style="
          border-radius: 10rpx;
          margin-left: 10rpx;
          margin-right: 10rpx;
          margin-top: 10rpx;
        "
      >
        <template v-slot:right>
          <uni-icons :type="iconsType" @click="iconsClick"></uni-icons>
        </template>
      </uni-section>
    </view>
    <uni-card v-if="isCardVisible && currentTabIndex !== 2">
      <view style="display: flex; justify-content: space-between">
        <text>物料编码: </text>
        <text>{{ item.materielCode }}</text>
      </view>
      <view style="display: flex; justify-content: space-between">
        <text>客户编码: </text>
        <text>{{ item.customerCode }}</text>
      </view>
      <view style="display: flex; justify-content: space-between">
        <text>物料描述: </text>
        <text>{{ item.materielName }}</text>
      </view>
      <view style="display: flex; justify-content: space-between">
        <text>库存地点: </text>
        <text>{{ item.inventoryLocation }}</text>
      </view>
      <view style="display: flex; justify-content: space-between">
        <text>单位: </text>
        <text>{{ item.unit }}</text>
      </view>
      <view style="display: flex; justify-content: space-between">
        <text>单据数量: </text>
        <text>{{ item.actualDeliveryQty }}</text>
      </view>
      <view style="display: flex; justify-content: space-between">
        <text>待出库数量: </text>
        <text style="color: red">{{
          item.actualDeliveryQty - item.scannedNum
        }}</text>
      </view>
    </uni-card>
    <view class="tabs-bg">
      <uv-tabs
        :list="tabList"
        @click="changeTabs"
        :current="currentTabIndex"
        :itemStyle="{ flex: 1, height: '44px' }"
      ></uv-tabs>
    </view>
    <!-- 扫码出库 -->
    <view v-if="currentTabIndex != 2" class="form">
      <uni-forms
        ref="baseFormRef"
        v-model="formData"
        :border="true"
        :label-width="100"
        :label-align="'left'"
      >
        <uni-data-checkbox
          v-model="formData.codeType"
          :localdata="codeTypeOption"
        ></uni-data-checkbox>

        <uni-forms-item label="库位" :border="true" name="storageCode">
          <uni-easyinput
            v-model="formData.storageCode"
            placeholder="请扫描目标库位码"
            :inputBorder="false"
            :placeholderStyle="`fontSize:12px;color:#999`"
            @change="storageCodeInputChange"
            @iconClick="storageCodeIconClick"
            prefixIcon="scan"
          ></uni-easyinput>
        </uni-forms-item>
        <uni-forms-item label="追溯码" :border="true" name="traceableCode">
          <uni-easyinput
            v-model="formData.traceableCode"
            placeholder="请扫描出库产品追溯码"
            :inputBorder="false"
            :placeholderStyle="`fontSize:12px;color:#999`"
            @change="traceableCodeInputChange"
            @iconClick="traceableCodeIconClick"
            prefixIcon="scan"
          ></uni-easyinput>
        </uni-forms-item>
        <uni-forms-item label="出库数量" :border="true" name="outboundQuantity">
          <uni-easyinput
            type="number"
            :inputBorder="false"
            v-model="formData.outboundQuantity"
            :placeholderStyle="`fontSize:12px;color:#999`"
            @change="outboundQuantityInputChange"
            :disabled="!isOutboundQuantityEnabled"
          ></uni-easyinput>
        </uni-forms-item>
      </uni-forms>

      <!-- 扫码出库 -->
      <view v-if="currentTabIndex === 0" style="display: flex; gap: 25px">
        <button style="flex: 3" type="default" @click="cxBtn">批次推荐</button>
        <button style="flex: 7" type="primary" @click="confirm">
          出库确认
        </button>
      </view>

      <!-- 撤销扫码 -->
      <view v-if="currentTabIndex === 1">
        <button type="warn" @click="revokeBtn">撤销扫码</button>
      </view>

      <!-- 扫码出库数据 -->
      <view v-if="currentTabIndex === 0" style="margin-top: 10rpx">
        <uni-table
          :border="true"
          stripe
          emptyText="暂无数据"
          style="border-radius: 20rpx"
        >
          <uni-tr>
            <uni-th width="200" align="center">追溯码</uni-th>
            <uni-th width="100" align="center">出库数量</uni-th>
          </uni-tr>
          <uni-tr v-for="(item, index) in tableData" :key="index">
            <uni-td align="center" style="word-break: break-all">{{
              item.traceableCode
            }}</uni-td>
            <uni-td align="center">{{ item.outboundQuantity }}</uni-td>
          </uni-tr>
        </uni-table>
      </view>
      <!-- 撤销扫码数据 -->
      <view v-if="currentTabIndex === 1" style="margin-top: 10rpx">
        <uni-table
          :border="true"
          stripe
          emptyText="暂无数据"
          style="border-radius: 20rpx"
        >
          <uni-tr>
            <uni-th width="200" align="center">追溯码</uni-th>
            <uni-th width="100" align="center">撤销数量</uni-th>
          </uni-tr>
          <uni-tr v-for="(item, index) in revokeTableData" :key="index">
            <uni-td align="center" style="word-break: break-all">{{
              item.traceableCode
            }}</uni-td>
            <uni-td align="center">{{ item.revokeQty }}</uni-td>
          </uni-tr>
        </uni-table>
      </view>
    </view>
    <!-- 撤销扫码 -->
    <!-- 历史扫码 -->
    <view v-if="currentTabIndex === 2" class="form">
      <view style="margin-top: 10rpx">
        <uni-data-select
          placeholder="请选择物料"
          v-model="queryData.materielName"
          :localdata="materielNameOption"
          @change="materielNameChange"
        ></uni-data-select>
      </view>
      <!-- 表格 -->
      <basic-table
        :columns="columns"
        :data="tableData1"
        :border="true"
        :stripe="true"
        emptyText="暂无数据"
      ></basic-table>
    </view>

    <u-toast ref="toastRef"></u-toast>
  </view>
</template>

<script>
import {
  getRecommendedBatches,
  getScanHistory,
  queryMaterialName,
  deliverySubmitForAPP,
  getItemFromTableByEntity,
  revokeScanForAPP,
} from "@/api/store/outbound/saleOut.js";
export default {
  data() {
    return {
      item: {}, //当前明细数据
      row: {}, //抬头数据
      rowData: {}, //所有明细数据
      iconsType: "down",
      isCardVisible: true,
      tabList: [
        {
          name: "扫码入库",
        },
        {
          name: "撤销扫码",
        },
        {
          name: "历史扫码",
        },
      ],
      currentTabIndex: 0,
      formData: {
        codeType: 0,
      },
      revokeFormData: {
        codeType: 0,
      },
      codeTypeOption: [
        {
          text: "批次码",
          value: 0,
        },
        {
          text: "序列号",
          value: 1,
          disable: true,
        },
        {
          text: "集成码",
          value: 2,
          disable: true,
        },
      ],
      //扫码出库
      tableData: [],
      //历史扫码抬头
      columns: [
        {
          fieldName: "traceableCode",
          fieldDesc: "追溯码",

          sorter: true,
          align: "center",
        },
        {
          fieldName: "revokeQty",
          fieldDesc: "撤销数量",
          sorter: true,
          align: "center",
        },
        {
          fieldName: "deliveryQty",
          fieldDesc: "出库数量",
          sorter: true,
          align: "center",
        },
      ],
      //历史扫码
      tableData1: [],
      //撤销扫码
      revokeTableData: [],
      queryData: {},
      materielNameOption: [],
      batchRecommendData: [],
      showBatchRecommend: false,
    };
  },
  onLoad: function (option) {
    const that = this;
    const eventChannel = this.getOpenerEventChannel();
    // console.log(JSON.parse(option.data), "option");
    eventChannel.on("row", function (row) {
      //   console.log(row, "row");
      that.row = row; //抬头数据
    });
    eventChannel.on("rowData", function (rowData) {
      that.rowData = rowData; //所有明细数据
      //   console.log(that.rowData, "rowData");
    });
    eventChannel.on("materielCode", function (data) {
      that.rowData.forEach((item) => {
        if (item.materielCode === data) {
          that.item = item; //当前明细数据
        }
      });
    });
  },
  computed: {
    isOutboundQuantityEnabled() {
      return !!this.formData.traceableCode && !!this.formData.storageCode;
    },
    pendingOutboundQuantity() {
      return Number(this.item.actualDeliveryQty) - Number(this.item.scannedNum);
    },
    navbarTitle() {
      if (this.currentTabIndex === 0) {
        return "销售出库拣货";
      } else if (this.currentTabIndex === 1) {
        return "销售出库拣货撤销";
      } else {
        return "销售出库拣货历史";
      }
    },
  },
  methods: {
    changeTabs(item) {
      this.currentTabIndex = item.index;
      if (item.index === 2) {
        this.queryAllScan();
      }
    },
    //返回
    toPath() {
      uni.navigateBack({
        delta: 1,
      });
    },
    iconsClick() {
      this.isCardVisible = !this.isCardVisible;
      this.iconsType = this.isCardVisible ? "down" : "up";
    },
    //库位
    storageCodeInputChange(val) {
      if (!val) return;
      this.formData.storageCode = val;
    },
    storageCodeIconClick() {
      const that = this;
      uni.scanCode({
        onlyFromCamera: true,
        success: function (val) {
          that.$set(that.formData, "storageCode", val.result);
        },
        fail() {
          that.$refs.toastRef.show({
            type: "error",
            message: "识别失败，请重新识别。",
          });
          that.$set(that.formData, "storageCode", "");
        },
      });
    },
    //追溯码
    traceableCodeInputChange(val) {
      //   console.log(this.item);

      if (!val) return;
      // 校验追溯码
      const codePrefix = val.split("#")[0]; //物料编码
      const custCode = val.split("#")[1]; //客户编码
      if (
        codePrefix !== this.item.materielCode &&
        custCode !== this.item.customerCode
      ) {
        this.$refs.toastRef.show({
          type: "error",
          message: "追溯码与物料编码/客户编码不匹配！",
        });
        this.formData.traceableCode = "";
        return;
      }
      this.formData.traceableCode = val;
    },
    traceableCodeIconClick() {
      const that = this;
      uni.scanCode({
        onlyFromCamera: true,
        success: function (val) {
          const code = val.result;
          //物料编码校验
          const codePrefix = code.split("#")[0];

          if (
            codePrefix !== this.item.materielCode &&
            custCode !== this.item.customerCode
          ) {
            this.$refs.toastRef.show({
              type: "error",
              message: "追溯码与物料编码/客户编码不匹配！",
            });
            that.$set(that.formData, "traceableCode", "");
            return;
          }
          that.$set(that.formData, "traceableCode", code);
        },
        fail() {
          that.$refs.toastRef.show({
            type: "error",
            message: "识别失败，请重新识别。",
          });
          that.$set(that.formData, "traceableCode", "");
        },
      });
    },
    //出库数量
    outboundQuantityInputChange(val) {
      if (!this.isOutboundQuantityEnabled) {
        this.formData.outboundQuantity = "";
        return;
      }
      if (!val) return;
      // 验证是否为整数
      if (!/^[1-9]\d*$/.test(val)) {
        this.$refs.toastRef.show({
          type: "warning",
          message: "请输入正整数。",
        });
        this.formData.outboundQuantity = "";
        return;
      }
      if (this.currentTabIndex === 0) {
        if (Number(val) > this.pendingOutboundQuantity) {
          this.$refs.toastRef.show({
            type: "warning",
            message: "出库数量不能大于待出库数量！",
          });
          this.formData.outboundQuantity = "";
          return;
        }
      }

      this.formData.outboundQuantity = val;
    },
    //批次推荐
    cxBtn() {
      getRecommendedBatches({
        materielCode: this.item.materielCode,
        customerCode: this.item.customerCode,
        factory: this.item.factory,
        inventoryLocation: this.item.inventoryLocation,
      })
        .then((result) => {
          this.showBatchRecommend = true;
          this.batchRecommendData = result.data;
          uni.navigateTo({
            url: `/pages/store/outbound/saleOut/batchs`,
            success: function (res) {
              res.eventChannel.emit("item", result.data);
            },
          });
          this.$refs.paging.complete(result.data);
        })
        .catch((result) => {
          this.$refs.paging.complete(false);
        });
    },
    //出库确认
    async confirm() {
      if (!/^[1-9]\d*$/.test(this.formData.outboundQuantity)) {
        this.$refs.toastRef.show({
          type: "warning",
          message: "请输入正整数。",
        });
        this.formData.outboundQuantity = "";
        return;
      }
      if (
        Number(this.formData.outboundQuantity) > this.pendingOutboundQuantity
      ) {
        this.$refs.toastRef.show({
          type: "warning",
          message: "出库数量不能大于待出库数量！",
        });
        this.formData.outboundQuantity = "";
        return;
      }
      const matCode = this.formData.traceableCode.split("#")[0]; //物料编码
      //   const custCode = this.item.split("#")[1]; //客户编码
      //根据物料编码获取保质期
      const { data: res } = await queryMaterialName(matCode);

      //   item: {}, //当前明细数据
      //   row: {}, //抬头数据
      //   rowData: {}, //所有明细数据
      let temp = {
        validateVo: {}, //校验对象
        reqVo: {}, //抬头
        itemsReqVo: [], //所有明细
      };
      temp.validateVo = {
        orderType: this.codeTypeOption.value,
        factory: this.item.factory,
        inventoryLocation: this.item.inventoryLocation,
        storageLocation: this.formData.storageCode,
        traceableCode: this.formData.traceableCode,
        shelfLife: res.shelfLife,
        num: Number(this.formData.outboundQuantity),
      };
      temp.reqVo = {
        ...this.row,
        inventoryLocation: this.item.inventoryLocation,
      };
      temp.itemsReqVo = this.rowData;

      const result = await deliverySubmitForAPP(temp);
      console.log(result, "result");
      if (result) {
        const row = {
          codeType: this.formData.codeType,
          traceableCode: this.formData.traceableCode,
          outboundQuantity: Number(this.formData.outboundQuantity),
        };
        // 将数据存入tableData
        const existingIndex = this.tableData.findIndex(
          (item) => item.traceableCode === row.traceableCode
        );
        if (existingIndex !== -1) {
          // 存在则累加数量
          this.tableData[existingIndex].outboundQuantity +=
            row.outboundQuantity;
        } else {
          // 不存在则添加新条目
          this.tableData.push(row);
        }
        //获取当前明细数据
        const detail = await getItemFromTableByEntity({
          deliveryNo: this.item.deliveryNo,
          materielCode: this.item.materielCode,
        });
        console.log(detail, "detail");

        if (detail.code === 200) {
          this.item.actualDeliveryQty = detail.data.actualDeliveryQty;
          this.item.scannedNum = detail.data.scannedNum;
        }

        this.formData.storageCode = "";
        this.formData.traceableCode = "";
        this.formData.outboundQuantity = "";

        this.$refs.toastRef.show({
          type: "success",
          message: "出库成功。",
        });
      }
    },
    //撤销扫码
    async revokeBtn() {
      if (!/^[1-9]\d*$/.test(this.formData.outboundQuantity)) {
        this.$refs.toastRef.show({
          type: "warning",
          message: "请输入正整数。",
        });
        this.formData.outboundQuantity = "";
        return;
      }
      // if (
      //   Number(this.formData.outboundQuantity ) > Number(this.item.scannedNum)
      // ) {
      //   this.$refs.toastRef.show({
      //     type: "warning",
      //     message: "撤销数量不能大于已出库数量！",
      //   });
      //   this.formData.outboundQuantity = "";
      //   return;
      // }
      const result = await revokeScanForAPP({
        orderType: this.codeTypeOption.value,
        factory: this.item.factory,
        inventoryLocation: this.item.inventoryLocation,
        storageLocation: this.formData.storageCode,
        traceableCode: this.formData.traceableCode,
        deliveryNo: this.item.deliveryNo,
        num: Number(this.formData.outboundQuantity),
      });
      if (result) {
        const row = {
          codeType: this.formData.codeType,
          traceableCode: this.formData.traceableCode,
          revokeQty: Number(this.formData.outboundQuantity),
        };
        //将数据存入revokeTableData
        const existingIndex = this.revokeTableData.findIndex(
          (item) => item.traceableCode === row.traceableCode
        );
        if (existingIndex !== -1) {
          // 存在则累加数量
          this.revokeTableData[existingIndex].revokeQty += row.revokeQty;
        } else {
          // 不存在则添加新条目
          this.revokeTableData.push(row);
        }
        //获取当前明细数据
        const detail = await getItemFromTableByEntity({
          deliveryNo: this.item.deliveryNo,
          materielCode: this.item.materielCode,
        });
        if (detail.code === 200) {
          this.item.actualDeliveryQty = detail.data.actualDeliveryQty;
          this.item.scannedNum = detail.data.scannedNum;
        }
        this.formData.storageCode = "";
        this.formData.traceableCode = "";
        this.formData.outboundQuantity = "";
        this.$refs.toastRef.show({
          type: "success",
          message: "撤销成功。",
        });
      }
    },
    //查询整单历史扫码
    queryAllScan() {
      getScanHistory({
        deliveryNo: this.item.deliveryNo,
        materielCode: this.item.materielCode,
      }).then((res) => {
        this.tableData1 = res.data;
      });
    },
    materielNameChange(val) {
      if (val) {
        const transferOrderNo = this.item.transferOrderNo;
        const materielCode = val;
        queryMaterielScan(transferOrderNo, materielCode).then((res) => {
          this.tableData1 = res.data;
        });
      } else {
        const transferOrderNo = this.item.transferOrderNo;
        queryAllScan(transferOrderNo).then((res) => {
          this.tableData1 = res.data;
        });
      }
    },
    
  },
};
</script>

<style lang="scss" scoped>
.lists {
  display: flex;
  justify-content: space-around;
  margin: 20rpx 0;
}

.tabs-bg {
  margin: 10rpx;
  border-radius: 10rpx;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  /* 让背景宽度100% */
  width: auto;
}

.form {
  flex: 1;
  margin: 10rpx;
  overflow: auto;
  padding: 5rpx 35rpx;
  border-radius: 10rpx;
  background-color: #ffffff;
}
</style>
