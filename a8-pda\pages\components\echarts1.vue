<script>
  import {
    HuanBaoPieAPI,
    AnQuanPieAPI,
    FinishingRateAPI,
  } from "@/api/index/statisticsWorkshop";
  import { TreeDeptSelectAPI } from "@/api/common";

  import heimcharts from "@/components/heimao-echart/heimao-echart";

  import * as echarts from "@/components/static/echarts.min";

  import { mapGetters } from "vuex";
  import { upload } from "@/utils/request";
  export default {
    name: "echarts1",
    computed: {
      ...mapGetters(["getRoleType"]),
    },
    components: {
      heimcharts,
    },
    data() {
      return {
        showTip: false,
        value: 0,
        position: [],
        list1: [
          {
            name: "环保稽查",
          },
          {
            name: "安全检查",
          },
          {
            name: "合格完成率",
          },
        ],
        index: 0,
        params: {
          beginTime: "",
          endTime: "",
        },
        params2: {
          beginTime: "",
          endTime: "",
        },
        params3: {
          beginTime: "",
          endTime: "",
          a: "",
        },
        /**组织架构 */
        dataTree: [],
        range: [],
        range2: [],
        range3: [],

        option: {},
      };
    },
    mounted(option) {
      console.log("----");
      // this.id = option.id;
      // console.log(this.init());
      this.getTime3();
      this.getAudit();
    },
    methods: {
      /**
       * 跳转
       * @param {*} val
       */
      toPath(val) {
        console.log("val", val);
        if (val == 1) {
          uni.navigateBack({
            delta: val,
          });
        } else {
          uni.navigateTo({
            url: "/pages/index/environmental/index",
          });
        }
      },
      /**
       * 组织架构
       */
      async getAudit() {
        const { data: res } = await TreeDeptSelectAPI();
        this.dataTree = res;
        console.log(this.dataTree);
      },

      /**
       * 环保
       */
      huanBao(data) {
        return {
          tooltip: {
            trigger: "item",
          },
          legend: {
            orient: "horizontal",
            bottom: "2%",
          },
          classes: "",
          label: {
            alignTo: "edge",
            formatter: "{c} ",
            minMargin: 5,
            edgeDistance: 10,
            lineHeight: 15,
            rich: {
              time: {
                fontSize: 10,
                color: "#999",
              },
            },
          },
          series: [
            {
              type: "pie",
              radius: "50%",
              data: data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
            },
          ],
        };
      },

      /**
       * 时间筛选环保
       */

      huanbaoChange(v) {
        this.params.beginTime = v[0];
        this.params.endTime = v[1];
        this.init();
      },

      /**
       * 环保
       */
      async init() {
        const { data: res } = await HuanBaoPieAPI(this.params);

        const option = this.huanBao(res);

        this.$refs.chart.init(echarts, (chart) => {
          // chart.setOption(this.option);

          // 监听tooltip显示事件
          chart.on("showTip", (params) => {
            this.showTip = true;
            console.log("showTip::");
          });
          chart.on("hideTip", (params) => {
            setTimeout(() => {
              this.showTip = false;
            }, 300);
          });

          this.$nextTick(() => {
            chart.setOption(option);
          });
        });
      },


      click(item) {
        console.log("----", item);
        this.index = item.index;
        if (this.index == 0) {
          this.init();
        }
        if (this.index == 1) {
          this.init3();
        }
        if (item.index == 2) {
          this.init2();
        }
      },
      /**
       * 获取当前时间+3
       */
      getTime3() {
        // 创建一个 Date 对象
        var today = new Date();

        // 获取年、月、日、时、分、秒
        var year = today.getFullYear();
        let arr = ["", ""];
        arr[0] = year + "-" + "01-01";
        arr[1] = year + "-" + "12-31";
        this.range = JSON.parse(JSON.stringify(arr));
        this.range2 = JSON.parse(JSON.stringify(arr));
        this.range3 = JSON.parse(JSON.stringify(arr));
        console.log(this.range, "this.range ");
        this.params.beginTime = arr[0];
        this.params.endTime = arr[1];
        this.params3.beginTime = arr[0];
        this.params3.endTime = arr[1];
        this.params2.beginTime = arr[0];
        this.params2.endTime = arr[1];
      },
      save() {
        this.$refs.chart.canvasToTempFilePath({
          success(res) {
            console.log("res::::", res);
          },
        });
      },
    },
  };
</script>
<template>
  <view class="look">
    <view style="height: 75vh; position: relative">
      <view class="datetime">
        <view class="title">时间选择 :</view>
        <uni-datetime-picker v-model="range" type="daterange" :clear-icon="false" @change="huanbaoChange" />
      </view>
      <view style="height: 70vh; position: relative">
        <heimcharts v-if="index == 0" ref="chart" @finished="init"></heimcharts>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .look {
    background-color: #fff;
    height: 100%;
    // overflow-y: auto;
  }

  .datetime {
    display: flex;
    justify-content: center;
    padding: 0 30rpx;
    margin-top: 10px;

    .title {
      display: flex;
      align-content: center;
      margin-right: 20rpx;
      width: 22%;
      text-align: right;
    }
  }

  /deep/ .u-tabs__wrapper__nav__item {
    flex: 1;
  }
</style>