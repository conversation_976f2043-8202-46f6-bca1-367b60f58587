<script>
	import navigationBar from "@/pages/components/navigation.vue";
	import { upload } from "@/utils/request";
	import {
		update,
		getExecutionItem,
		changeExecutionToConfirm
	} from "@/api/index/inspection";
	export default {
		component: {
			navigationBar,
		},
		data() {
			return {
				current: 0,
				virtualList: [],
				dataList: [],
				taskOption: {},
				resultTypeOption: [{
						text: '合格',
						value: '0'
					},
					{
						text: '不合格',
						value: '1'
					}
				],
				formData: {
					resultType: '0',
				},
				fileList: [],
				showFinishModal: false,
			};
		},

		onLoad(option) {
			this.taskOption = option
		},

		methods: {
			query(pageNo, pageSize) {
				let params = this.taskOption
				getExecutionItem(pageNo, pageSize, params).then(res => {
					this.virtualList = res.data.records;
					this.taskOption.taskId = res.data.records[0].taskId;
					this.$refs.paging.complete(res.data.records);
				}).catch(res => {
					this.$refs.paging.complete(false);
				})
			},
			virtualListChange(vList) {
				this.virtualList = vList;
			},

			//返回
			toPath() {
				uni.navigateBack({
					delta: 1
				})
			},
			//结果类型映射
			getResultTypeLabel(value) {
				const option = this.resultTypeOption.find(item => item.value === value);
				return option ? option.text : value;
			},
			clickCard(row) {
				if (row.fillPicture) {
					 try {
					    const parsed = JSON.parse(row.fillPicture);
					    this.fileList = Array.isArray(parsed) 
					      ? parsed.map(url => ({ url })) 
					      : [];
					  } catch (e) {
					    this.fileList = []; 
					  }
					} else {
					  this.fileList = []; 
				}
				this.formData = {
					...row,
					resultType: row.resultType !== undefined ? row.resultType : '0',
				}
				this.$refs.inputDialog.open();
			},
			//录入确定
			dialogInputConfirm() {
				this.formData.status = 2,
				this.formData.fillPicture = JSON.stringify(
				  this.fileList.length == 0 
				    ? [] 
				    : this.fileList.map((t) => t.url)
				);
					update(this.formData).then(res => {
						if (res.code === 200) {
							this.$refs.inputDialog.close();
							this.$refs.paging.reload();
						} else {
							this.$refs.toastRef.show({
								type: "error",
								message: "录入失败！"
							})
						}
					})
			},
			closeDialog() {
				this.$refs.inputDialog.close();
			},
			// 新增图片
			async afterRead(event) {
			  let lists = [].concat(event.file);
			  let fileListLen = this[`fileList`].length;
			  lists.map((item) => {
			    this[`fileList`].push({
			      ...item,
			      status: "uploading",
			      message: "上传中",
			    });
			  });
			  for (let i = 0; i < lists.length; i++) {
			    try {
			      const { data: result } = await upload(lists[i].url);
			      let item = this[`fileList`][fileListLen];
			      this[`fileList`].splice(
			        fileListLen,
			        1,
			        Object.assign(item, {
			          status: "success",
			          message: "",
			          url: result.url,
			        })
			      );
			
			      fileListLen++;
			    } catch (err) {}
			  }
			},
			// 删除图片
			deletePic(event) {
			  this[`fileList`].splice(event.index, 1);
			},
			//录入完成
			finishBtn() {
				this.showFinishModal = true;
			},
			onFinishConfirm() {
				changeExecutionToConfirm(this.taskOption.taskId).then(res => {
					if (res.code === 200) {
						this.showFinishModal = false;
						this.toPath()
					} else {
						this.$refs.toastRef.show({
							type: "error",
							message: "录入失败！"
						})
					}
				})
				
				
			}
		},
	};
</script>

<template>
	<z-paging ref="paging" v-model="dataList" @query="query" @virtualListChange="virtualListChange" use-virtual-list
		:force-close-inner-list="true" cell-height-mode="dynamic">
		<view>
			<u-navbar title="点检执行录入" @leftClick="toPath(1)" bgColor="#4669ea" placeholder></u-navbar>
		</view>


		<view :id="`zp-id-${item.zp_index}`" :key="item.zp_index" v-for="(item, index) in virtualList">
			<uni-card @click="clickCard(item)" style="border-radius: 20rpx">
				<view class="uni-body">
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>设备编号：</text>
							<text class="blue-value">{{ taskOption.deviceNo }}</text>
						</view>
						<view class="card-body-col">
							<text>项目序号：</text>
							<text class="blue-value">{{ index + 1 }}</text>
						</view>
					</view>
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>检查部位：</text>
							<text>{{ item.checkLocation }}</text>
						</view>
						<view class="card-body-col">
							<text>点检内容：</text>
							<text>{{ item.inspectionContent }}</text>
						</view>
					</view>
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>检验标准：</text>
							<text>{{ item.inspectionStandard }}</text>
						</view>
						<view class="card-body-col">
							<text>检验工具：</text>
							<text>{{ item.inspectionTools }}</text>
						</view>
					</view>
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>最大值：</text>
							<text>{{ item.maxVal }}</text>
						</view>
						<view class="card-body-col">
							<text>最小值：</text>
							<text>{{ item.minVal }}</text>
						</view>
					</view>
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>检验方法：</text>
							<text>{{ item.inspectionMethod }}</text>
						</view>
						<view class="card-body-col">
							<text>结果类型：</text>
							<text>{{ getResultTypeLabel(item.resultType) }}</text>
						</view>
					</view>
				</view>
			</uni-card>
		</view>
		<template #bottom>
			<view>
				<button class="finish-btn" @click="finishBtn">完 成</button>
			</view>
		</template>

		<view>
			<uni-popup ref="inputDialog" type="dialog" :mask-click="false">
				<view class="dialog-card">
					<view class="dialog-table">
						<view class="dialog-row1">
							<view class="dialog-label">检查部位：</view>
							<view class="dialog-value">{{ formData.checkLocation }}</view>
							<view class="dialog-label">点检内容：</view>
							<view class="dialog-value">{{ formData.inspectionContent }}</view>
						</view>
						<view class="dialog-row">
							<view class="dialog-label">检验标准：</view>
							<view class="dialog-value">{{ formData.inspectionStandard }}</view>
							<view class="dialog-label">最大值：</view>
							<uni-easyinput class="dialog-input" v-model="formData.maxVal" placeholder="请输入最大值" />

						</view>
						<view class="dialog-row">
							<view class="dialog-label">检验工具：</view>
							<view class="dialog-value">{{ formData.inspectionTools }}</view>
							<view class="dialog-label">最小值：</view>
							<uni-easyinput class="dialog-input" v-model="formData.minVal" placeholder="请输入最小值" />
						</view>
						<view class="dialog-row">
							<view class="dialog-label">检验方法：</view>
							<view class="dialog-value">{{ formData.inspectionMethod }}</view>
							<view class="dialog-label">结果类型：</view>
							<uni-data-select placeholder="结果类型" v-model="formData.resultType"
								:localdata="resultTypeOption" @change="resultTypeChange"></uni-data-select>
						</view>
						<view class="dialog-row">
							<view class="dialog-label">图片：</view>
							<view class="p-upload">
							  <u-upload
							    :fileList="fileList"
							    @afterRead="afterRead"
							    @delete="deletePic"
							    name="file"
							    multiple
							    :maxCount="9"
							  >
							    <p-svg size="60" src="/static/SVG/upload.svg" />
							  </u-upload>
							</view>
						</view>
					</view>
					<view class="dialog-actions">
						<button type="primary" @click="dialogInputConfirm">确定</button>
						<button @click="closeDialog">取消</button>
					</view>
				</view>
			</uni-popup>
			<!-- uView模态框 -->
			<u-modal
				:show="showFinishModal"
				title="警告"
				content="该任务将转为确认状态而且不可逆，请仔细检查是否完成所有任务，是否确认？"
				show-cancel-button
				confirm-text="确定"
				cancel-text="取消"
				confirm-color="#4669ea"
				@confirm="onFinishConfirm"
				@cancel="showFinishModal = false"
			/>
		</view>
		<u-toast ref="toastRef"></u-toast>
	</z-paging>

</template>

<style lang="scss" scoped>
	.dialog-card {
		background: #fff;
		border-radius: 16rpx;
		padding: 25rpx 20rpx 20rpx 20rpx;
		min-width: 600rpx;
	}

	.dialog-table {
		width: 100%;
	}

	.dialog-row1 {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.dialog-row {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.dialog-label {
		width: 150rpx;
		color: #333;
		font-size: 25rpx;
		text-align: right;
	}

	.dialog-value {
		width: 100rpx;
		color: #222;
		font-size: 25rpx;
		margin-right: 25rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.dialog-input {
		width: 100rpx;
	}

	.dialog-actions {
		display: flex;
		justify-content: flex-end;
		gap: 16rpx;
		margin-top: 18rpx;
	}

	.dialog-actions button {
		width: 90rpx;
		height: 60rpx;
		font-size: 25rpx;
		padding: 0;
	}

	.uni-body {
		font-size: 14px;
		font-weight: 400;
		line-height: 22px
	}

	.card-body-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 15rpx;
		font-family: 'PingFang-SC-Light';
	}

	.card-body-row2 {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10rpx;
	}

	.card-body-col {
		display: flex;
		align-items: center;
		gap: 4rpx;
		width: 48%;
		font-family: 'PingFang-SC-Light';
	}

	.blue-value {
		color: #2e8ff4;
		font-weight: 500;
	}

	.finish-btn {
		width: 180rpx;
		height: 80rpx;
		background: #4669ea;
		color: #fff;
		border: none;
		border-radius: 30rpx;
		font-size: 30rpx;
		font-weight: bold;
		margin: 15rpx auto 15rpx auto;
		display: block;
	}
</style>