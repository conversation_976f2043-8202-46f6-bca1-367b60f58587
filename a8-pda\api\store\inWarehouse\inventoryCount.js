import request from "@/utils/request"

export function GetListAPI(current, size, params) {
  return request({
    url: '/wms/inventoryCheck/listForAPP',
    method: 'GET',
    params: {
        pageNum: current,
        pageSize: size,
    	...params
    }
  });
}

export function GetItemListByInventoryNoAPI(inventoryNo) {
  return request({
    url: '/wms/inventoryCheckItem/getListByInventoryNo/' + inventoryNo,
    method: 'GET',
  });
}

export function InventoryCheckScanSubmitAPI(data) {
  return request({
    url: '/wms/inventoryCheck/scanSubmit',
    method: 'POST',
    data
  });
}

export function GetInventoryLogAPI(data) {
  return request({
    url: '/wms/inventoryCheck/getLogsByInventoryNo',
    method: 'POST',
    data
  });
}

export function FinishedAPI(data) {
  return request({
    url: '/wms/inventoryCheck/finished',
    method: 'POST',
    data
  });
}