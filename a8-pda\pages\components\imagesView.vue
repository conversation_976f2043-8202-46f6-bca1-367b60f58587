<template>
  <view class="u-page">
    <view class="u-demo-block">
      <view class="u-demo-block__content">
        <view class="album">
          <view class="album__avatar"> </view>
          <view class="album__content">
            <u-album :urls="fileList" rowCount="3" multipleSize="100"></u-album>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { upload } from '@/utils/request'
export default {
  data() {
    return {
      fileList: [],
    }
  },

  methods: {},
  /**
   * 接受参数
   * @param {*} options
   */
  onLoad(options) {
    console.log(options, 'options')
    this.fileList = options.imgList.split(',') || []
  },
}
</script>
<style lang="scss">
.album {
  @include flex;
  align-items: flex-start;

  &__avatar {
    background-color: $u-bg-color;
    padding: 5px;
    border-radius: 3px;
  }

  &__content {
    margin-left: 10px;
    flex: 1;
  }
}
</style>
