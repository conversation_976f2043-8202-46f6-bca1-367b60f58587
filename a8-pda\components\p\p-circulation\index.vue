<script>
import view from "../../../pages/message/view.vue";
export default {
  components: { view },
  props: ["list", "fillCode"],

  data() {
    return {
      teamsList: [], //班组下拉
      teamsFrom: {
        teamId: "",
        teamUser: "",
        teamUserIds: [],
      }, //班组信息
      teams: [],
      dataObj: {},
      checkboxList: [],
      rules: {
        teamId: {
          type: "string",
          required: true,
          message: "请选择班组",
          trigger: ["blur", "change"],
        },
        teamUserIds: {
          type: "array",
          required: true,
          message: "请选择派工人",
          trigger: ["blur", "change"],
        },
      },
    };
  },

  methods: {
    getText(t) {
      let dayName = "";
      switch (t.status) {
        case "1":
          dayName = t.fillName + "进行保存";
          break;
        case "2":
          dayName = t.fillName + "进行填报";
          break;
        case "3":
          dayName = t.fillName + "进行处理";
          break;
        case "4":
          dayName = t.fillName + "进行确认";

          break;
        case "5":
          dayName = t.fillName + "进行关结";
          break;
        case "6":
          dayName =   "超时";
          break;
        case "7":
          dayName = t.fillName + "进行驳回";
          break;
      }
      return dayName;
    },
    turnoverReason(t) {
      let dayName = "";
      switch (t.status) {
        case "1":
          dayName = "";
          break;
        case "2":
          dayName = "故障描述:" + t.turnoverReason;
          break;
        case "3":
          dayName = "故障分析:" + t.turnoverReason;
          break;
        case "4":
          dayName = "";

          break;
        case "5":
          dayName = "关结原因:" + t.turnoverReason;
          break;
        case "6":
          dayName = "";
          break;
        case "7":
          dayName = "驳回:" + t.turnoverReason;
          break;
      }
      return dayName;
    },
  },
  /**
   * 获取班组下拉
   */
  async mounted() {},
};
</script>
<template>
  <view class="p-circulation">
    <view class="circulation_title"> 维修单号：{{ fillCode }}</view>
    <view class="circulation">
      <view class="circulation-content">
        <view
          class="circulation-content-list"
          v-for="(t, index) in list"
          :key="index"
        >
          <view class="drop"> </view>
          <view class="info">
            <view class="titme"> {{ getText(t) }}</view>
            <!-- <view class="titme"> {{ "填报人：" }}</view> -->
            <view class="titme"> 操作时间：{{ t.fillTime }}</view>
            <view class="describe"> {{ turnoverReason(t) }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.circulation_title {
  padding: 20rpx;
}
.circulation {
  height: 100%;
  background-color: #f6f6f6;
  padding: 20rpx;
  overflow-y: auto;

  .circulation-content {
    border-left: 1px solid #e8e9e9;
    .circulation-content-list {
      display: flex;
    }
    .drop {
      width: 10rpx;
      height: 10rpx;
      background-color: #4aa2fe;
      border-radius: 10rpx;
      margin-top: 20rpx;
      margin-right: 20rpx;
    }
    .info {
      border-radius: 10rpx;
      flex: 1;
      //   height: 400rpx;
      background-color: #fff;
      padding: 20rpx;
      margin-bottom: 20rpx;
      .titme {
        padding: 5rpx 0;
        font-size: 14px;
        color: #8d8d8d;
        // border-bottom: 1px solid #dddddd;
      }
      .describe {
        padding: 5rpx 0;
        font-size: 14px;
      }
    }
  }
}
</style>