<script>
import { GetInstructDdlAPI } from '@/api/common'
export default {
  props: ['list'],
  data() {
    return {
      instructList: [], //班组下拉
      teamsFrom: {
        teamId: '',
        teamUser: '',
        actualInstruction: [],
      }, //班组信息
      teams: [],
      dataObj: {},
      checkboxList: [],
      rules: {
        actualInstruction: {
          type: 'array',
          required: true,
          message: '请选择实际指令',
          trigger: ['blur', 'change'],
        },
      },
    }
  },

  methods: {
    /**
     * 处理数据
     */
    getStr(v) {
      let str = this.instructList
        .filter((item2) => {
          return v.some((item1) => item1 === item2.id)
        })
        .map((t) => t.instructInfo)
        ?.join(',')
      this.teams = this.instructList.filter((item2) => {
        return v.some((item1) => item1 === item2.id)
      })

      this.$set(this.teamsFrom, 'teamUser', str)
    },

    /**
     * 选择人员
     */
    checkboxChange(v) {
      this.getStr(v)
    },

    /**
     * 获取指令
     */
    async getInstruct() {
      const { data: res } = await GetInstructDdlAPI()
      this.instructList = res
    },

    /**
     * 提交
     */
    async subMit() {
      const res = await this.$refs.uForm.validate()
      console.log(res, 'res', this.teamsFrom)
      if (!res) return false
      return this.teamsFrom
    },
  },
  /**
   * 获取班组下拉
   */
  async mounted() {
    console.log(this.list.length, 'list')
    if (this.list.length != 0) {
      this.teamsFrom.actualInstruction = this.list
      const { data: res } = await GetInstructDdlAPI()
      this.instructList = res
      this.getStr(this.list)
    } else {
      this.getInstruct()
    }
    // if(this.list)
  },
}
</script>
<template>
  <view class="p-teams">
    <u--form labelPosition="left" :model="teamsFrom" :rules="rules" ref="uForm">
      <u-form-item>
        <view class="teamUser">
          <view class="teamUser_box" v-for="t in teams" :key="t.id">{{
            t.instructInfo
          }}</view>
        </view>
      </u-form-item>
      <u-form-item prop="actualInstruction" borderBottom>
        <u-checkbox-group
          style="margin-top: 20px"
          v-model="teamsFrom.actualInstruction"
          placement="column"
          @change="checkboxChange"
        >
          <u-checkbox
            :customStyle="{ marginBottom: '8px' }"
            v-for="(item, index) in instructList"
            :key="index"
            :label="item.instructInfo"
            :name="item.id"
            :value="item.value"
          >
          </u-checkbox>
        </u-checkbox-group>
      </u-form-item>
    </u--form>
  </view>
</template>

<style lang="scss" scoped>
.p-teams {
  width: 100%;
  height: 50vh;
}
.teamUser {
  border: 1px solid #dadada98;
  border-radius: 5px;
  height: 50px;
  min-height: 30px;
  // min-width: 280px;
  width: 100%;
  padding: 5px;
  padding: 5px;
  margin-top: 10px;
  border-radius: 5px;
  display: flex;
  flex-wrap: wrap;
  .teamUser_box {
    padding: 0 5px;
    margin: 0 5px;
    height: 25px;
    line-height: 25px;
    margin-bottom: 5px;
    border-radius: 5px;
    background-color: #e9f5fd;
    color: #239ceb;
  }
}
</style>
