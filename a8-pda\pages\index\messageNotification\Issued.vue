<script>
import {
  GetTreeSelectAPI,
  GetRoleAPI,
  SubmitSendAPI,
} from "@/api/index/messageNotification";

import LyTree from "@/components/ly-tree/ly-tree.vue";
export default {
  components: {
    LyTree,
  },
  data() {
    return {
      ready: false, // 这里用于自主控制loading加载状态，避免异步正在加载数据的空档显示“暂无数据”
      treeData: [],
      tabs: [
        {
          name: "组织架构",
          value: 1,
        },
        {
          name: "角色",
          value: 2,
        },
      ],
      roleIdAll: "",
      disabled: false,
      roleList: [],
      roleId: [],
      Roledata: [],

      form: {},
      active: 1,
      data: [],
    };
  },
  onLoad(o) {
    this.form = JSON.parse(o.data);
    this.getTreeSelect();
    this.getRole();
  },
  methods: {
    /**
     * 返回
     */
    toPath(v) {
      if (v == 1) {
        uni.navigateBack({
          delta: v,
        });
      }
    },

    /**
     *选中后触发事件
     */
    handCheck(obj) {
      this.data = this.$refs.tree.getCheckedNodes(false, false);
    },

    /**
     * 删除
     */
    remov(t, i) {
      this.$refs.tree.setChecked(t.id, false);
      this.data.splice(i, 1);
    },
    /**
      清空
       */
    clear() {
      let that = this;
      if (this.active == 1) {
        uni.showModal({
          title: "清空",
          content: "确定清空已选内容",
          icon: "error",
          success: function (res) {
            if (res.confirm) {
              if (that.data.length == 0) {
                that.$refs.uToast.show({
                  message: "暂无已选",
                });
              } else {
                that.data.map((t) => {
                  that.$refs.tree.setChecked(t.id, false);
                });
                that.data = [];
              }
            } else if (res.cancel) {
              console.log("用户点击取消");
            }
          },
        });
      } else {
        uni.showModal({
          title: "清空",
          content: "确定清空已选内容",
          icon: "error",
          success: function (res) {
            if (res.confirm) {
              if (that.Roledata.length == 0) {
                that.$refs.uToast.show({
                  message: "暂无已选",
                });
              } else {
                that.Roledata = [];
                that.roleId = [];
                that.roleIdAll = [];
              }
            } else if (res.cancel) {
              console.log("用户点击取消");
            }
          },
        });
      }
    },

    /*获取树结构*/
    async getTreeSelect() {
      const { data: res } = await GetTreeSelectAPI();
      this.treeData = res;
      this.ready = true;
    },

    /**获取角色 */
    async getRole() {
      const { data: res } = await GetRoleAPI();

      this.roleList = res;
    },

    /**
     * 角色
     */
    roleChange(v) {
      let arr = [];
      this.roleList.map((t) => {
        v.map((val) => {
          if (val == t.id) {
            arr.push(t);
          }
        });
      });

      if (v.length != this.roleList) {
        this.roleIdAll = [];
      }
      this.Roledata = arr;
    },

    /**
     * 删除角色
     */
    removRole(item, index) {
      this.Roledata.splice(index, 1);

      this.roleId.map((t, i) => {
        if (item.id == t) {
          this.roleId.splice(i, 1);
        }
      });
    },

    /**全选 */
    allChange(item) {
      console.log(item);
      if (item.length == 0) {
        this.roleId = [];
        this.Roledata = [];
      } else {
        this.roleId = this.roleList.map((t) => t.id);
        this.Roledata = this.roleList;
      }
    },

    /**下发 */
    async submit() {
      if (this.roleId.length == 0 && this.data.length == 0) {
        this.$refs.uToast.show({
          message: "请最少选择一个角色或者组织架构",
        });
        return;
      }
      if (this.data.length != 0) {
        this.form.deptIds = this.data.map((t) => t.id);
        this.form.checkType = 1;
      } else {
        this.form.roleIds = this.roleId;
        this.form.checkType = 2;
      }

      await SubmitSendAPI(this.form);
      uni.showToast({
        title: "下发成功",
        duration: 2000,
      });
      setTimeout(() => {
        uni.switchTab({
          url: "/pages/index/index",
        });
      }, 1000);
    },

    /**递归处理数据 */
    recursion(arr, disabled) {
      arr.forEach((el) => {
        if (el.children) {
          console.log();
          el.disabled = disabled;
          this.recursion(el.children, disabled);
        } else {
          el.disabled = disabled;
        }
      });
    },
  },

  /**
   *监听
   */
  watch: {
    /**监听是否勾选组织架构 */
    data(news, old) {
      console.log(news, "wes");
      if (news.length > 0) {
        let arr = JSON.parse(JSON.stringify(this.roleList));
        arr.map((t) => {
          t.disabled = true;
        });
        this.roleList = arr;
        this.disabled = true;
      } else {
        let arr = JSON.parse(JSON.stringify(this.roleList));
        arr.map((t) => {
          t.disabled = false;
        });
        this.roleList = arr;
        this.disabled = false;
      }
    },

    /*监听角色*/
    Roledata(news, old) {
      if (news.length > 0) {
        this.recursion(this.treeData, true);
      } else {
        this.recursion(this.treeData, false);
      }
    },

    /**监听tabs切换 */
    active(news, old) {
      if (news == 1) {
        console.log("=", this.data);
        let that = this;
        this.$nextTick(() => {
          this.data.map((t) => {
            console.log(that.$refs.tree, "t", this);
            that.$refs.tree.setChecked(t.id, true);
          });
        });
      }
    },
  },
};
</script>
<template>
  <view class="lssued">
    <u-navbar
      title="知会"
      bgColor="#5375ff"
      placeholder
      titleStyle="color:#ffffff"
      height="44px"
      @leftClick="toPath(1)"
    >
    </u-navbar>

    <view class="tab">
      <view
        class="tab-list"
        :class="{ show: active == item.value }"
        v-for="item in tabs"
        :key="item"
        @click="active = item.value"
      >
        {{ item.name }}
      </view>
    </view>

    <view v-if="active == 1">
      <view class="tree">
        <view class="height-tree">
          <ly-tree
            :tree-data="treeData"
            :ready="ready"
            ref="tree"
            :checkStrictly="false"
            :checkOnClickNode="true"
            :expandOnClickNode="false"
            node-key="id"
            currentNodeKey="1"
            :showCheckbox="true"
            @check="handCheck"
          >
          </ly-tree>
        </view>
        <view class="data-list height-tree">
          <view style="margin-bottom: 10rpx">已选择数据：</view>
          <view v-for="(item, index) in data" :key="item.id" class="box">
            <view class="data-list-text">
              {{ item.label }}
            </view>

            <u-icon
              name="close"
              color="#b8041b"
              @click="remov(item, index)"
            ></u-icon>
          </view>

          <u-empty v-if="data.length == 0" mode="data" class="empty"> </u-empty>
        </view>
      </view>
    </view>
    <view v-else>
      <view class="checkbox">
        <view class="height-tree">
          <u-checkbox-group
            v-model="roleIdAll"
            placement="column"
            @change="allChange"
          >
            <u-checkbox
              :customStyle="{ marginBottom: '8px' }"
              :label="'全选'"
              :disabled="disabled"
              :name="0"
            >
            </u-checkbox>
          </u-checkbox-group>
          <u-checkbox-group
            v-model="roleId"
            placement="column"
            @change="roleChange"
          >
            <u-checkbox
              :customStyle="{ marginBottom: '8px' }"
              v-for="(item, index) in roleList"
              :key="index"
              :label="item.label"
              :disabled="item.disabled"
              :name="item.id"
            >
            </u-checkbox>
          </u-checkbox-group>
        </view>
        <view class="data-list height-tree">
          <view style="margin-bottom: 10rpx">已选择数据：</view>
          <view v-for="(item, index) in Roledata" :key="item.id" class="box">
            <view class="data-list-text"> {{ item.label }}</view>

            <!-- <u-icon
              name="close"
              color="#b8041b"
              @click="removRole(item, index)"
            ></u-icon> -->
            <view class="remov" @click="removRole(item, index)">x</view>
          </view>

          <u-empty v-if="Roledata.length == 0" mode="data" class="empty">
          </u-empty>
        </view>
      </view>
    </view>

    <view class="button">
      <u-button type="success" style="width: 40%" @click="submit()"
        >确认下发</u-button
      >
      <u-button type="success" color="#f14849" style="width: 40%" @click="clear"
        >清除已选</u-button
      >
    </view>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<style lang="scss" scoped>
.lssued {
  height: 100vh;
  background-color: #fff;
  position: relative;
}

.tab {
  padding: 30rpx;
  display: flex;

  .tab-list {
    flex: 1;
    text-align: center;
    border: 1px solid rgb(180, 180, 180);
    padding: 20rpx;
  }
}

.button {
  padding: 20rpx;
  display: flex;
  margin-top: 40rpx;
  position: absolute;
  bottom: 3%;
  left: 50%;
  width: 80%;
  transform: translateX(-50%);
}

.remov {
  color: #b8041b;
  font-size: 16px;
}
.data-list {
  padding: 20rpx;
  position: relative;

  .data-list-text {
    width: 70%;
    overflow: hidden;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
  }
  .empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.box {
  padding: 10rpx;
  font-size: 14px;
  color: #717377;
  border: 1px solid rgb(196, 196, 196);
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.height-tree {
  max-height: 60vh;
  overflow: hidden;
  overflow-y: auto;
  width: 50%;
}
.tree {
  display: flex;
}
.checkbox {
  display: flex;
  padding: 0 30rpx;
}
.data-list {
  width: 40%;
  height: 70vh;
  border-left: 1px solid rgb(151, 151, 151);
}

.show {
  background-color: #778de6;
  color: #ffff;
}
</style>