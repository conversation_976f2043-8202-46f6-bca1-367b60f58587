<script>
import { GetListAPI } from '@/api/index/workOrder'
import maintenanceList from './index.vue'

export default {
  components: {
    maintenanceList,
  },
  data() {
    return {
      list: [
        {
          name: '全部',
          status: 0,
        },

        {
          name: '待派工',
          status: 1,
        },
        {
          name: '待维保',
          status: 2,
        },
        {
          name: '维保中',
          status: 3,
        },
        {
          name: '待提交',
          status: 4,
        },
        {
          name: '待审核',
          status: 5,
        },
        {
          name: '未通过',
          status: 6,
        },
        {
          name: '已完成',
          status: 7,
        },
      ],
      // 分页查询
      query: {
        pageNum: 1,
        pageSize: 5,
        // 搜索框绑定字段
        queryParam: '',
        mineType: 0,
      },
      // 加载状态
      status: 'nomore',
      //   列表数据
      tableData: [],
      total: 0,
      // 字典
      dicts: {},
      // 搜索弹窗
      popupShow: false,
      // 是否出现tabs
      isTabs: false,
      // 是否出现筛选狂
      screenShow: false,
      // 搜索出的列表
      selectList: [
        {
          text: '22',
        },
      ],

      // 提交审核告警弹窗
      subExamineShow: false,
      // 转单弹窗
      transferShow: false,
      // 派工弹窗
      workShow: false,
      // 勾选后的数据
      checkArr: [],
      // 勾选后的状态
      statusArr: [],
      // 派工加载
      workLoading: false,
      selectedValue: '2',
      teamGroupList: [
        {
          text: '张三',
          value: '1',
        },
        {
          text: '张5',
          value: '2',
        },
      ],
      // 已选择的派工人显示
      workCheckValueShow: [
        {
          label: '张三',
          value: '1',
        },
        {
          label: '张5',
          value: '2',
        },
        {
          label: '张三',
          value: '1',
        },
        {
          label: '张5',
          value: '2',
        },
        {
          label: '张三',
          value: '1',
        },
        {
          label: '张5',
          value: '2',
        },
        {
          label: '张三',
          value: '1',
        },
        {
          label: '张5',
          value: '2',
        },
      ],
      // 已选择的派工人
      userValue: [],
      // 派工人列表
      workList: [],
    }
  },
  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      this.tableData = []
      this.query.pageNum = 1
      await this.getList()
    } catch (err) {
    } finally {
      uni.stopPullDownRefresh()
    }
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  async onReachBottom() {
    if (this.$refs.maintenance.status == 'nomore') return

    this.query.pageNum = ++this.query.pageNum
  },
  onLoad() {
    // this.dicts = uni.$u.useDict("maintenance_work_order_status");
    // this.list = this.dicts.maintenance_work_order_status.map((t) => ({
    //   name: t.label,
    //   value: t.value,
    // }));
  },
  methods: {
    /**
     * 标签页切换时触发 ( 0:全部 1:待派工 2:维保中 3:待审核 4:维保完成 )
     * @param {*} item
     */
    tabsChange(item) {
      this.query.status = item.status
      this.tableData = []

      this.getList()
    },

    /**
     * 筛选重置
     */
    screenReset() {
      this.screenShow = false
      this.query.mineType = 1
      this.tableData = []
      this.getList()
    },
    /**
     * 筛选完成
     */
    screenComplete() {
      this.screenShow = false
      this.tableData = []
      this.getList()
    },
    /**
     * 维保执行
     * @description:待维保状态调起扫一扫界面，扫描设备二维码后跳转执行维保界面
     * 维保中、未通过、待提交：跳转执行维保界面；
     */
    carry(item) {
      if (item.status == 1) {
        uni.scanCode({
          onlyFromCamera: true,
          success: function (res) {
            console.log('条码类型：' + res.scanType)
            console.log('条码内容：' + res.result)
            if (res.scanTyp != 'qrCode')
              uni.showToast({
                title: '请正确扫描二维码',
              })
            // 扫描二维码，判断设备是否与维保工单绑定设备一致，否则提示“扫描失败！请认准设备扫描”回到上一页；
            if (res.result != 'qrCode')
              uni.showToast({
                title: '请认准设备扫描，或确认您是否是当前单据的维保人！',
              })
            // 跳转到维保执行
            uni.navigateTo({
              url: `/pages/index/workOrder/carryWork?id=${item.id}&result=${res.result}`,
            })
          },
        })
      } else {
        // 跳转执行维保界面
        uni.navigateTo({
          url: `/pages/index/workOrder/carryWork?id=${item.id}`,
        })
      }
    },
    /**
     * @description:提交审核
     */
    subExamine() {
      uni.showToast({
        title: '已提交',
        icon: 'success',
      })
      this.subExamineShow = false
    },
    /**
     * @description:转单
     */
    transfer() {
      uni.showToast({
        title: '转单成功',
        icon: 'success',
      })
      this.transferShow = false
    },

    /**
     * @description:派工
     */
    work() {
      if (this.userValue.length < 1) {
        uni.showToast({
          title: '至少选择一个员工',
          icon: 'error',
        })
        return
      }
      uni.showToast({
        title: '派工成功',
        icon: 'success',
        success: this.cancelData,
      })
    },

    /**
     * 复选勾选
     */
    checkChange(val) {
      console.log(val)
      const arr = this.tableData.filter((t) => val.includes(t.id))
      this.checkArr = arr
    },

    /**
     * 清空选中数据
     */
    cancelData() {
      this.transferShow = false
      this.workShow = false
      this.workCheckValueShow = []
      this.userValue = []
      // this.checkArr = [];
    },

    /**
     * 搜索
     */
    search() {
      // this.screenShow = false
      // this.getList()
      this.isTabs = true
    },
    /**
     * 点击搜索出来的列表
     * @param {*} item
     */
    selectClick(item) {
      this.isTabs = true
    },

    /**
     * 跳转 isTabs = false
     * @param {*} val
     */
    toPath(val) {
      if (this.isTabs) return (this.isTabs = false)
      if (val == 1) {
        uni.navigateBack({
          delta: val,
        })
      } else {
        uni.navigateTo({
          url: val,
        })
      }
    },
    /**
     * 点击搜索
     */
    focusChange() {
      this.isTabs = false
      this.screenShow = false
    },
    /**
     * 当分页为第一页时
     */
    pageChange(v) {
      this.query.pageNum = 1
    },
  },
}
</script>

<template>
  <view>
    <view class="header">
      <u-icon class="back" size="25" name="arrow-left" @click="toPath(1)" />
      <u-input
        class="search"
        prefixIcon="search"
        v-model="query.queryParam"
        focus
        @focus="focusChange"
      />
      <view class="select-text" @click="search" v-if="!isTabs">搜索</view>
    </view>

    <!-- 提示列表 -->
    <!-- <view class="tipList" v-if="!isTabs">
      <view
        class="tipList-item"
        v-for="(t, i) in selectList"
        :key="i"
        @click="selectClick(t)"
        >{{ t.text }}</view
      >
    </view> -->
    <maintenanceList
      v-if="isTabs"
      :type="'搜索'"
      ref="maintenance"
      :condition="query.queryParam"
      :pageNum="query.pageNum"
      @pageChange="pageChange"
    ></maintenanceList>
  </view>
</template>

<style lang="scss" scoped>
.flex-items {
  width: 180px;
  .title {
    margin-left: 10px;
  }
}

.info-box {
  margin-top: 20px;
}
.content {
  width: 100%;
  margin-top: 20px;
  display: flex;
  align-items: center;
  .content-item {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .label {
      color: #747474;
    }
  }
}

.bottom-items {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  view {
    font-size: 12px;
  }
}

.header {
  padding-top: 50px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 10px;
  border-bottom: 1px solid #000;
  .back {
    flex: 0.3;
  }
  .search {
    flex: 3;
  }
  .select-text {
    flex: 0.4;
    text-align: center;
    color: #1fb213;
  }
}

.tipList {
  background-color: #fff;
  .tipList-item {
    display: flex;
    align-items: center;
    padding: 10px;

    .label {
      color: #747474;
    }
  }
}

.tabsBox {
  background-color: #ffffff;
  border-left: 0 solid #000;
  border-right: 0 solid #000;
  border-bottom: 0 solid #000;
  position: relative;
  .screen-box {
    position: absolute;
    width: 100vw;
    top: 40px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    .screen-content {
      height: 100px;
      width: 100vw;
      padding: 20px;
      background-color: #ffffff;
      border-bottom: 1px solid #000;
      .screen-my {
        width: 80px;
        height: 10px;
        color: #fff;
        padding: 10px;
        cursor: pointer;
        line-height: 10px;
        text-align: center;
        border-radius: 5px;
        background: #d1d1d1;
        border: 1px solid #d1d1d1;
      }
      .screen-my-av {
        background: #007aff;
        border: 1px solid #007aff;
      }
    }
    .screen-bottom {
      display: flex;
      justify-content: space-between;
    }
  }
}

.workBy {
  border: 1px solid #ebebeb;
  min-height: 30px;
  min-width: 200px;
  padding: 5px;
  margin-top: 10px;
  border-radius: 5px;
  flex-wrap: wrap;
  .workByItem {
    padding: 0 5px;
    margin: 0 5px;
    margin-bottom: 5px;
    border-radius: 5px;
    background-color: #e9f5fd;
    color: #239ceb;
  }
}
</style>
