<script>
export default {
  props: [
    "show", //控制弹窗
    "versio",
    "appURL",
  ],
  data() {
    return {
      modalShow: true,
      percentageShow: false,
      percentage: 0,
      showConfirmButton: true,
    };
  },

  methods: {
    /**确定更新 */
    confirm() {
      this.percentageShow = true;
      this.showConfirmButton = false;

      let that = this;
      const downloadTask = uni.downloadFile({
        url: this.appURL,
        success(res) {
          if (res.statusCode === 200) {
            const filePath = res.tempFilePath;
            that.installUpdate(filePath);
          } else {
            console.error("下载更新包失败");
          }
        },
        fail(err) {
          console.error("下载更新包失败", err);
        },
      });

      downloadTask.onProgressUpdate((res) => {
        this.percentage = res.progress;
      });

      // this.modalShow = false;
      // this.$emit("change", false);
    },
    /*安装*/
    installUpdate(filePath) {
      this.modalShow = false;
      this.$emit("change", false);
      plus.runtime.install(filePath, {
        force: false,
        success(res) {
          console.log("安装成功");
          plus.runtime.restart();
        },
        fail(err) {
          console.error("安装失败", err);
        },
      });
    },
    /*取消更新*/
    cancel() {
      this.modalShow = false;
      this.$emit("change", false);
    },
  },

  mounted() {
    console.log("texte");
  },
  watch: {},
};
</script>
<template>
  <view class="p-update">
    <u-modal
      :show="modalShow"
      @confirm="confirm"
      :showConfirmButton="showConfirmButton"
      :showCancelButton="showConfirmButton"
      confirmText="确定"
      cancelText="取消"
      @cancel="cancel"
    >
      <view v-if="!percentageShow"> 是否更新到{{ versio }}版本 </view>
      <view v-else style="witdh: 300px">
        <view style="500px">更新中,请耐心等待.....</view>
        <u-line-progress
          :percentage="percentage"
          activeColor="#3c9cff"
        ></u-line-progress>
      </view>
    </u-modal>
  </view>
</template>
