import request from "@/utils/request"

// 入库列表
export function GetTransferInListAPI(current, size, params) {
  return request({
    url: '/wms/inventoryTransfer/getTransferInList',
    method: 'GET',
    params: {
        pageNum: current,
        pageSize: size,
    	...params
    }
  });
}

// 入库明细
export function GetTransferInDetailsAPI(transferOrderNo) {
  return request({
    url: '/wms/inventoryTransfer/getTransferInDetails',
    method: 'GET',
    params: { transferOrderNo }
  });
}

//入库确认
export function SaveInboundDataAPI(data) {
  return request({
    url: '/wms/inventoryTransfer/saveInbound',
    method: 'POSt',
    data
  });
}

//同步ERP
export function InboundConfirmAPI(data) {
  return request({
    url: '/wms/inventoryTransfer/confirmInbound',
    method: 'POST',
    data
  });
}

//入库历史扫码
export function queryAllScan(transferOrderNo) {
  return request({
    url: '/wms/inventoryTransfer/queryInAllScan',
    method: 'GET',
    params: { transferOrderNo }
  });
}

export function queryMaterielScan(transferOrderNo, materielCode) {
  return request({
    url: '/wms/inventoryTransfer/queryInMaterielScan',
    method: 'GET',
    params: { transferOrderNo, materielCode }
  });
}