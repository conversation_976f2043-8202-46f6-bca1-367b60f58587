<script>
	import navigationBar from "@/pages/components/navigation.vue";
	import {
		TreeTwoAPI,
		TreeThreeAPI,
		getConfirmList,
		selectDeviceTypeno,
		appConfirmOneClickExecution
	} from "@/api/index/upkeep";
	export default {
		component: {
			navigationBar,
		},
		data() {
			return {
				current: 0,
				virtualList: [],
				dataList: [],
				queryData: {},
				deviceTypeOption: [],
				workShopOption: [],
				workShopShow: false,
				productionLineOption: [],
				productionLineShow: false,
				productionLineMap: new Map(),
				showFinishModal: false,
			};
		},

		onShow() {
			this.queryproductionLineOption();
			this.selectDeviceType();
			this.queryWorkShopOption();
			if (this.$refs.paging) {
				this.$refs.paging.reload();
			}
		},

		methods: {
			query(pageNo, pageSize) {
				let params = {}
				const {
					deviceNo,
					validDate,
					deviceTypename,
					workshopId,
					productionLineId
				} = this.queryData
				if (deviceNo) {
					params.deviceNo = deviceNo
				}
				if (validDate) {
					params.validDate = validDate
				}
				if (deviceTypename) {
					params.deviceTypename = deviceTypename
				}
				if (workshopId) {
					params.workshopId = workshopId
				}
				if (productionLineId) {
					params.productionLineId = productionLineId
				}
				getConfirmList(pageNo, pageSize, params).then(res => {
					this.virtualList = res.data.records;
					this.$refs.paging.complete(res.data.records);
				}).catch(res => {
					this.$refs.paging.complete(false);
				})
			},
			virtualListChange(vList) {
				this.virtualList = vList;
			},

			//返回
			toPath() {
				uni.navigateBack({
					delta: 1
				})
			},
			//扫码
			scanCode() {
				const that = this
				uni.scanCode({
					onlyFromCamera: true,
						success: function (res) {
							that.$set(that.queryData, "deviceNo", res.result);
						},
						fail() {
							that.$refs.toastRef.show({
								type: "error",
								message: "识别失败，请重新识别。"
							})
						}
				})
			},
			//任务时间选择
			validDateChange(val) {
				this.queryData.validDate = val
			},
			//设备类型
			selectDeviceType() {
				selectDeviceTypeno().then(res => {
					this.deviceTypeOption = res.data.map(item => ({
						text: item.deviceTypename,
						value: item.deviceTypename
					}));
				})
			},
			deviceTypeChange(val) {
				this.queryData.deviceTypename = val
			},
			/**
			 * 车间
			 */
			//获取车间数据并将键映射为组件所需键
			queryWorkShopOption() {
				TreeTwoAPI().then(res => {
					const formatData = (data) => {
						return data.map(item => ({
							text: item.title,
							value: item.id,
							children: item.children ? formatData(item.children) : []
						}))
					}
					this.workShopOption = formatData(res.data)
				})
			},
			workShopClick() {
				this.workShopShow = true
			},
			workShopChange(val) {
				this.query.workshopId = val.detail.value[1].value
			},
			/**
			 * 产线
			 */
			//获取产线数据并将键映射为组件所需键
			queryproductionLineOption() {
				TreeThreeAPI().then(res => {
					const formatData = (data) => {
						return data.map(item => ({
							text: item.title,
							value: item.id,
							children: item.children ? formatData(item.children) : []
						}))
					}
					this.productionLineOption = formatData(res.data)
					this.createProductionLineMap(this.productionLineOption)
				})
				
			},
			//递归创建映射
			createProductionLineMap(nodes) {
				nodes.forEach(node => {
					if (node.children && node.children.length > 0) {
						this.createProductionLineMap(node.children);
					} else {
						//仅处理叶子节点（第三级）
						this.productionLineMap.set(node.value, node.text);
					}
				});
			},
			//根据产线id在创建的隐射里查找产线名称
			findProductionLineText(id) {
				return this.productionLineMap.get(id) || id;
			},
			productionLineClick() {
				this.productionLineShow = true
			},
			productionLineChange(val) {
				this.query.workshopId = val.detail.value[2].value
			},
			//搜索
			search() {
				this.$refs.paging.reload();
			},
			//清空
			clear() {
				this.queryData = [],
				this.$refs.paging.reload();
			},
			clickCard(row) {
				uni.navigateTo({
							url: `/pages/device/upkeep/confirm/finish?
							taskNo=${row.taskNo}&taskOrder=${row.taskOrder}&deviceNo=${row.deviceNo}`
				})
			},
			finishBtn() {
				if(this.queryData.validDate) {
					this.showFinishModal = true
					
				} else {
					this.$refs.toastRef.show({
						type: "error",
						message: "请先选择任务日期！"
					})
				}
			},
			onFinishConfirm() {
				appConfirmOneClickExecution(this.queryData).then(res => {
					if (res.code === 200) {
					  this.showFinishModal = false;
					  this.$refs.paging.reload();
					} else {
					  this.$refs.toastRef.show({
					    type: "error",
					    message: "一键执行失败！",
					  });
					}
				})
			}
		},
	};
</script>

<template>
	<z-paging ref="paging" v-model="dataList" @query="query" @virtualListChange="virtualListChange" use-virtual-list
		:force-close-inner-list="true" cell-height-mode="dynamic">
		<view>
			<u-navbar title="保养确认" @leftClick="toPath()" bgColor="#4669ea" placeholder></u-navbar>
		</view>
		<view class="search">
			<view class="search-row">
				<uni-easyinput v-model="queryData.deviceNo" placeholder="设备编号" border="surround" prefixIcon="scan"
					@iconClick="scanCode">
				</uni-easyinput>
				<uni-datetime-picker placeholder="任务日期" type="date" v-model="queryData.validDate"
					@change="validDateChange"></uni-datetime-picker>
				<uni-data-select placeholder="设备类型" v-model="queryData.deviceTypename" :localdata="deviceTypeOption"
					@change="deviceTypeChange"></uni-data-select>
			</view>
			<view class="search-row">
				<uni-data-picker :show="workShopShow" placeholder="车间" popup-title="请选择车间" :localdata="workShopOption"
					v-model="queryData.workshopId" @change="workShopChange" @nodeclick="workShopClick">
				</uni-data-picker>

				<uni-data-picker :show="productionLineShow" placeholder="产线" popup-title="请选择产线"
					:localdata="productionLineOption" v-model="queryData.productionLineId"
					@change="productionLineChange" @nodeclick="productionLineClick">
				</uni-data-picker>

				<u-button style="width: 55px; height: 35px; margin-left: 0px" type="success" text="搜索"
					@click="search"></u-button>
				<u-button style="
            width: 55px;
            height: 35px;
            margin-left: 12px;
          " type="error" :plain="true" @click="clear" text="清空">
				</u-button>
			</view>
		</view>

		<view :id="`zp-id-${item.zp_index}`" :key="item.zp_index" v-for="(item, index) in virtualList">
			<uni-card @click="clickCard(item)" style="border-radius: 20rpx">
				<view class="uni-body">
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>任务单号：</text>
							<text class="blue-value">{{ item.taskNo }}</text>
						</view>
						<view class="card-body-col">
							<text>任务节点号：</text>
							<text class="blue-value">{{ item.taskOrder }}</text>
						</view>
					</view>
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>设备编号：</text>
							<text>{{ item.deviceNo }}</text>
						</view>
						<view class="card-body-col">
							<text>设备名称：</text>
							<text>{{ item.deviceName }}</text>
						</view>
					</view>
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>产线：</text>
							<text>{{ findProductionLineText(item.productionLineId) }}</text>
						</view>
						<view class="card-body-col">
							<text>任务日期：</text>
							<text>{{ item.validDate }}</text>
						</view>
					</view>
				</view>
			</uni-card>
		</view>
		<template #bottom>
				<button class="finish-btn" @click="finishBtn">一 键 执 行</button>
		</template>
		
		<!-- uView模态框 -->
		<u-modal
			:show="showFinishModal"
			title="警告"
			content="该任务将转为确认状态而且不可逆，请仔细检查是否完成所有任务，是否确认？"
			show-cancel-button
			confirm-text="确定"
			cancel-text="取消"
			confirm-color="#4669ea"
			@confirm="onFinishConfirm"
			@cancel="showFinishModal = false"
		/>
		
		<u-toast ref="toastRef"></u-toast>
	</z-paging>
	
</template>

<style lang="scss" scoped>
	.uni-body {
		font-size: 14px;
		font-weight: 400;
		line-height: 22px
	}

	.card-body-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 15rpx;
		font-family: 'PingFang-SC-Light';
	}

	.search {
		width: 93%;
		margin: 10rpx 10rpx 10rpx 12rpx;
		display: flex;
		flex-direction: column;
		gap: 15rpx;
		background-color: #ffffff;
		padding: 15rpx;
		border-radius: 20rpx;
		box-shadow: 0 10rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.search-row {
		display: flex;
		gap: 10rpx;
	}

	.card-body-row2 {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10rpx;
	}

	.card-body-col {
		display: flex;
		align-items: center;
		gap: 4rpx;
		width: 48%;
		font-family: 'PingFang-SC-Light';
	}

	.blue-value {
		color: #2e8ff4;
		font-weight: 500;
	}
	.finish-btn {
		width: 200rpx;
		height: 80rpx;
		background: #4669ea;
		color: #fff;
		border: none;
		border-radius: 30rpx;
		font-size: 30rpx;
		font-weight: bold;
		margin: 15rpx auto 15rpx auto;
		display: block;
	}
</style>