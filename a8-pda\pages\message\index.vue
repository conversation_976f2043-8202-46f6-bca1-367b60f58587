<script>
// import { GetListAPI } from "@/api/index/message";

export default {
  data() {
    return {
      list: [
        {
          name: "待处理",
          status: 1,
        },

        {
          name: "已处理",
          status: 2,
        },

        {
          name: "暂停",
          status: 3,
        },
        {
          name: "抄送",
          status: 4,
        },
      ],
      form: {},

      // 分页查询
      query: {
        pageNum: 1,
        pageSize: 5,
      },
      // 加载状态
      status: "loading",
      //   列表数据
      tableData: [],
      single: "",
      total: 0,
      // 字典
      dicts: {},

      teamGroupList: [
        {
          text: "张三",
          value: "1",
        },
        {
          text: "张5",
          value: "2",
        },
      ],

      indixAcita: "1",
      show: false,
      userValue: [],
      checkboxValue: [],
      // 派工人列表
      workList: [],
    };
  },
  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      this.tableData = [];
      this.status = "loading";
      this.query.pageNum = 1;
      await this.getList();

      this.$set(this.list, 0, obj);
    } catch (err) {
    } finally {
      uni.stopPullDownRefresh();
    }
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  async onReachBottom() {},
  async onLoad() {},
  onShow() {
    this.screenComplete();
  },
  methods: {
    /**
     * 筛选重置
     */
    async screenReset() {
      this.screenShow = false;
      this.query.mineType = 0;
      this.tableData = [];
      this.query.pageNum = 1;
      this.status = "loading";
      await this.getList();
    },
    /**
     * 筛选完成
     */
    async screenComplete() {
      this.query.pageNum = 1;
      this.status = "loading";
      this.tableData = [];

      this.getList();
    },
    /**
     * 手动下拉
     */
    async scrolltolower() {
      if (this.status == "nomore") return;
      this.status = "loading";
      this.query.pageNum = ++this.query.pageNum;
      await this.getList();
    },

    /**
     * tabs切换
     */
    tabsChane(v) {
      this.indixAcita = v.status;
      this.query.status = v.status;

      this.search();
    },
    /**
     * 搜索
     */
    async search() {
      this.query.pageNum = 1;
      this.query.pageSize = 5;

      this.status = "loading";
      this.tableData = [];

      this.getList();
    },
    /**
     * 获取维保列表
     */
    async getList() {
      // const { data: result } = await GetListAPI(this.query);
      /**
       * 解决页面不更新
       */
      // this.$nextTick(() => {
      //   this.tableData = this.tableData.concat(result.result);
      //   this.total = result.total;
      //   if (this.tableData.length >= this.total) {
      //     this.status = "nomore";
      //   }
      // });
    },

    /**
     * 跳转
     * @param {*} val
     */
    toPath(val) {
      console.log(val, "--");
      uni.navigateTo({
        url: `/pages/message/view?id=${val}`,
      });
    },
  },

  /**
   * 监听
   */
  watch: {},
};
</script>

<template>
  <view class="page">
    <u-navbar
      title="消息通知"
      bgColor="#5375ff"
      placeholder
      titleStyle="color:#ffffff"
      height="44px"
    >
    </u-navbar>

    <view class="box">
      <u-list @scrolltolower="scrolltolower">
        <u-list-item v-for="(t, i) in tableData" :key="i">
          <view class="box-list" @click="toPath(t.id)">
            <view class="inco">
              <u-image
                width="80rpx"
                height="80rpx"
                :src="
                  t.notificationType == 1
                    ? '../../static/images/gongao.png'
                    : '../../static/images/renwuG.png'
                "
              ></u-image>
            </view>
            <view class="box-content">
              <view class="content" v-if="t.notificationType == 1">
                <view>
                  <u-cell title="公告消息" isLink :border="false">
                    <text
                      slot="value"
                      class="red"
                      v-if="t.readMark == 0"
                    ></text>
                  </u-cell>
                  <u-cell title="公告单号：" isLink :border="false">
                    <text slot="value" class="u-slot-value">{{
                      t.taskNo || "--"
                    }}</text>
                  </u-cell>
                  <u-cell title="日期：" isLink :border="false">
                    <text slot="value" class="u-slot-value">{{
                      t.createTime || "--"
                    }}</text>
                  </u-cell>
                  <u-cell title="发布人：" isLink :border="false">
                    <text slot="value" class="u-slot-value">{{
                      t.publisherBy || "--"
                    }}</text>
                  </u-cell>
                  <u-cell title="" isLink :border="false">
                    <view slot="value" class="ellipsis-value">
                      {{ t.title }}
                    </view>
                  </u-cell>
                  <view></view>
                </view>
              </view>
              <view class="content" v-else>
                <view>
                  <u-cell title="任务通知" isLink :border="false">
                    <text
                      slot="value"
                      class="red"
                      v-if="t.readMark == 0"
                    ></text>
                  </u-cell>
                  <u-cell title="通知单号：" isLink :border="false">
                    <text slot="value" class="u-slot-value">{{
                      t.taskNo
                    }}</text>
                  </u-cell>
                  <u-cell title="日期：" isLink :border="false">
                    <text slot="value" class="u-slot-value">{{
                      t.createTime || "--"
                    }}</text>
                  </u-cell>
                  <u-cell title="任务来源：" isLink :border="false">
                    <text slot="value" class="u-slot-value">{{
                      t.taskSource
                    }}</text>
                  </u-cell>
                  <u-cell title="发布人" isLink :border="false">
                    <text slot="value" class="u-slot-value">{{
                      t.publisherBy
                    }}</text>
                  </u-cell>
                  <u-cell title="隐患类型" isLink :border="false">
                    <text slot="value" class="u-slot-value">{{
                      t.hiddenDangerType
                    }}</text>
                  </u-cell>
                </view>
              </view>
            </view>
          </view>
        </u-list-item>
        <!-- <u-loadmore :status="status" /> -->
      </u-list>
    </view>

    <!-- </p-bottom> -->
  </view>
</template>

<style lang="scss" scoped>
.flex-items {
  width: 180px;

  .title {
    margin-left: 10px;
  }
}
.red {
  width: 5px;
  height: 5px;
  background-color: red;
  border-radius: 50%;
}

/deep/ .u-cell__bod {
  padding: 3px 15px !important;
}
.ellipsis-value {
  text-overflow: ellipsis; /* 溢出显示省略号 */
  overflow: hidden; /* 溢出隐藏 */
  max-width: 300px;
  white-space: nowrap; /* 强制不换行 */
}
.identifying {
  color: red;
}
.box-content {
  flex: 1;
}
.titles {
  min-width: 100px;
  max-width: 170px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin: 0 5px;
}
.inco {
  margin-top: 20px;
  margin-left: 5px;
}
.box-list {
  display: flex;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
}

/deep/ .u-search__action {
  color: #fff !important;
}

.page {
  height: calc(100vh - 0px);
  background-color: #5375ff;

  display: flex;
  flex-direction: column;
}

.box {
  box-sizing: border-box;
  background: #f1f3f7;
  padding-top: 10rpx;
  // min-height: 100%;

  border-radius: 30rpx;
  flex: 1;
  overflow-y: auto;
  padding-bottom: 150rpx;
}

.indixAcita {
  border: 1px solid #fff;
  border-radius: 35rpx;
}

.search {
  width: 80%;
  // display: flex;
  margin: 10rpx auto;
}

.bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  height: 150rpx;
  width: 100%;
  border-top: 1px solid #747474;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tabs {
  height: 100rpx;
  display: flex;
  justify-content: space-evenly;
  background-color: #5375ff;
  align-items: center;

  .tabs-list {
    color: #fff;
    padding: 1rpx 0rpx;
    width: 17%;
    display: flex;
    font-size: 25rpx;
    align-items: center;
    height: 50%;
    justify-content: center;
    position: relative;

    .badge {
      top: 0;
      position: absolute;
      right: -10%;
    }
  }
}

.top {
  background-color: #5375ff;
}

/deep/ .u-icon__icon {
  display: none !important;
}

/deep/ .u-navbar__content {
  // background-color: rgba(0, 0, 0, 0) !important;
}

.info-box {
  margin-top: 20px;
}

.content {
  width: 100%;
  margin-top: 20px;

  .content-item {
    display: flex;
    align-items: center;
    margin-left: 10px;
    width: 100%;

    .label {
      color: #747474;
    }
  }
}

.bottom-items {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  align-items: center;
}

.tabsBox {
  background-color: #ffffff;
  border: 1px solid #000;
  border-left: 0 solid #000;
  border-right: 0 solid #000;
  position: relative;

  .screen-box {
    position: absolute;
    width: 100vw;
    top: 40px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;

    .screen-content {
      height: 100px;
      width: 100vw;
      padding: 20px;
      background-color: #ffffff;
      border-bottom: 1px solid #000;

      .screen-my {
        width: 80px;
        height: 10px;
        color: #fff;
        padding: 10px;
        cursor: pointer;
        line-height: 10px;
        text-align: center;
        border-radius: 5px;
        background: #d1d1d1;
        border: 1px solid #d1d1d1;
      }

      .screen-my-av {
        background: #007aff;
        border: 1px solid #007aff;
      }
    }

    .screen-bottom {
      display: flex;
      justify-content: space-between;
    }
  }
}

.workBy {
  border: 1px solid #ebebeb;
  min-height: 30px;
  min-width: 200px;
  padding: 5px;
  margin-top: 10px;
  border-radius: 5px;
  flex-wrap: wrap;

  .workByItem {
    padding: 0 5px;
    margin: 0 5px;
    margin-bottom: 5px;
    border-radius: 5px;
    background-color: #e9f5fd;
    color: #239ceb;
  }
}
</style>