<template>
	<view class="page">
		<view class="form">

			<view>
				<u-navbar title="成品移库" @leftClick="toPath()" bgColor="#4669ea" placeholder></u-navbar>
			</view>

			<uni-forms ref="baseFormRef" v-model="formData" :border="true" :label-width="100" :label-align="'left'">
				<uni-data-checkbox v-model="formData.codeType" :localdata="codeTypeOption"></uni-data-checkbox>

				<uni-forms-item label="移出库位" :border="true" name="outStorageCode">
					<uni-easyinput v-model="formData.outStorageCode" placeholder="请扫描目标库位码" :inputBorder="false"
						:placeholderStyle="`fontSize:12px;color:#999`" @change="outStorageCodeInputChange"
						@iconClick="outStorageCodeIconClick" @clear="outStorageCodeClear"
						prefixIcon="scan"></uni-easyinput>
				</uni-forms-item>
				<uni-forms-item label="追溯码" :border="true" name="traceableCode">
					<uni-easyinput v-model="formData.traceableCode" placeholder="请扫描出库产品追溯码" :inputBorder="false"
						:placeholderStyle="`fontSize:12px;color:#999`" @change="traceableCodeInputChange"
						@iconClick="traceableCodeIconClick" @clear="traceableCodeClear" prefixIcon="scan"
						:disabled="!isTraceableCodeEnabled"></uni-easyinput>
				</uni-forms-item>
				<uni-forms-item label="移库数量" :border="true" name="outQuantity">
					<uni-easyinput type="digit" :inputBorder="false" v-model="formData.outQuantity"
						:placeholderStyle="`fontSize:12px;color:#999`" @change="outQuantityInputChange"
						:disabled="!isOutQuantityEnabled"></uni-easyinput>
				</uni-forms-item>
				<uni-forms-item label="移入库位" :border="true" name="inStorageCode">
					<uni-easyinput v-model="formData.inStorageCode" placeholder="请扫描目标库位码" :inputBorder="false"
						:placeholderStyle="`fontSize:12px;color:#999`" @change="inStorageCodeInputChange"
						@iconClick="inStorageCodeIconClick" prefixIcon="scan"
						:disabled="isInStorageCodeDisabled"></uni-easyinput>
				</uni-forms-item>
			</uni-forms>

			<view class="btn-row">
				<button class="btn-submit" type="primary" @click="submit">保存</button>
				<button class="btn-submit" type="primary" @click="confirm" :disabled="!isConfirmEnabled">确认移库</button>
			</view>

			<!-- 表格 -->
			<view>
				<uni-table :border="true" stripe emptyText="暂无数据" style="border-radius: 20rpx;">
					<uni-tr>
						<uni-th width="100" align="center">追溯码</uni-th>
						<uni-th width="100" align="center">移动数量</uni-th>
						<uni-th width="100" align="center">物料描述</uni-th>
					</uni-tr>
					<uni-tr v-for="(item, index) in tableData" :key="index">
						<uni-td align="center" style="word-break: break-all;">{{item.traceableCode}}</uni-td>
						<uni-td align="center">{{item.outQuantity}}</uni-td>
						<uni-td align="center">{{item.materielName}}</uni-td>
					</uni-tr>
				</uni-table>
			</view>
			<u-toast ref="toastRef"></u-toast>
		</view>
	</view>
</template>

<script>
	import {
		storageCodeExists,
		cpTraceableCodeExists,
		cpOutQuantity,
		queryMaterialName,
		cpLocationMove
	} from '@/api/store/inWarehouse/locationMovement'
	export default {
		data() {
			return {
				codeTypeOption: [{
					text: '批次码',
					value: 0
				}, {
					text: '序列号',
					value: 1,
					disable: true
				}, {
					text: '集成码',
					value: 2,
					disable: true
				}],
				currentTabIndex: 0,
				formData: {
					codeType: 0,
					outStorageCode: '',
					inStorageCode: '',
					traceableCode: '',
					outQuantity: '',
					materialName: ''
				},
				tableData: []
			};
		},
		computed: {
			isTraceableCodeEnabled() {
				return !!this.formData.outStorageCode;
			},
			isInStorageCodeDisabled() {
				return this.tableData.length > 0;
			},
			isOutQuantityEnabled() {
				return !!this.formData.traceableCode;
			},
			isConfirmEnabled() {
				return this.tableData.length > 0;
			}
		},
		methods: {
			//返回
			toPath() {
				uni.navigateBack({
					delta: 1,
				});
			},
			//移出库位
			outStorageCodeInputChange(val) {
				if (!val) return;
				storageCodeExists(val).then(res => {
					if (res.data === true) {
						this.formData.outStorageCode = val
					} else {
						this.$refs.toastRef.show({
							type: "warning",
							message: "请录入正确的库位。"
						});
						this.formData.outStorageCode = ''
					}
				})
			},
			outStorageCodeIconClick() {
				const that = this
				uni.scanCode({
					onlyFromCamera: true,
					success: function(val) {
						storageCodeExists(val.result).then(res => {
							if (res.data === true) {
								that.$set(that.formData, "outStorageCode", val.result)
							} else {
								that.$refs.toastRef.show({
									type: "warning",
									message: "请录入正确的库位。"
								});
								that.$set(that.formData, "outStorageCode", '')
							}
						})
					},
					fail() {
						that.$refs.toastRef.show({
							type: "error",
							message: "识别失败，请重新识别。"
						})
					}
				})
			},
			outStorageCodeClear() {
				this.formData.outStorageCode = '';
				this.formData.traceableCode = '';
				this.formData.outQuantity = '';
			},
			//追溯码
			traceableCodeInputChange(val) {
				if (!val) return;
				cpTraceableCodeExists(val).then(res => {
					if (res.data === true) {
						this.formData.traceableCode = val
					} else {
						this.$refs.toastRef.show({
							type: "warning",
							message: "请录入正确的追溯码。"
						});
						this.formData.traceableCode = ''
					}
				})
			},
			traceableCodeIconClick() {
				if (!this.isTraceableCodeEnabled) {
					this.$refs.toastRef.show({
						type: "warning",
						message: "请先扫描移出库位"
					});
					return;
				}
				const that = this
				uni.scanCode({
					onlyFromCamera: true,
					success: function(val) {
						cpTraceableCodeExists(val.result).then(res => {
							if (res.data === true) {
								that.$set(that.formData, "traceableCode", val.result)
							} else {
								that.$refs.toastRef.show({
									type: "warning",
									message: "请录入正确的追溯码。"
								});
								that.$set(that.formData, "traceableCode", '')
							}
						})
					},
					fail() {
						that.$refs.toastRef.show({
							type: "error",
							message: "识别失败，请重新识别。"
						})
					}
				})
			},
			traceableCodeClear() {
				this.formData.traceableCode = '';
				this.formData.outQuantity = '';
			},
			//移库数量
			outQuantityInputChange(val) {
				if (!this.isOutQuantityEnabled) {
					this.formData.outQuantity = '';
					return;
				}
				if (!val) return;
				// 验证是否为整数
				if (!/^[1-9]\d*$/.test(val)) {
					this.$refs.toastRef.show({
						type: "warning",
						message: "请输入正整数。"
					});
					this.formData.outQuantity = '';
					return;
				}
				const quantity = parseInt(val);
				cpOutQuantity(this.formData.outStorageCode, this.formData.traceableCode).then(res => {
					if (res.data.quantity < quantity) {
						this.$refs.toastRef.show({
							type: "warning",
							message: "移库数量不可大于库存数量。"
						});
						this.formData.outQuantity = ''
					} else {
						this.formData.outQuantity = val
					}
				})
			},
			//移入库位
			inStorageCodeInputChange(val) {
				if (!val) return;
				storageCodeExists(val).then(res => {
					if (res.data === true) {
						this.formData.inStorageCode = val
					} else {
						this.$refs.toastRef.show({
							type: "warning",
							message: "请录入正确的库位。"
						});
						this.formData.inStorageCode = ''
					}
				})
			},
			inStorageCodeIconClick() {
				const that = this
				uni.scanCode({
					onlyFromCamera: true,
					success: function(val) {
						storageCodeExists(val.result).then(res => {
							if (res.data === true) {
								that.$set(that.formData, "inStorageCode", val.result)
							} else {
								that.$refs.toastRef.show({
									type: "warning",
									message: "请录入正确的库位。"
								});
								that.$set(that.formData, "inStorageCode", '')
							}
						})
					},
					fail() {
						that.$refs.toastRef.show({
							type: "error",
							message: "识别失败，请重新识别。"
						})
					}
				})
			},
			//保存事件
			submit() {
				if (this.formData.outStorageCode &&
					this.formData.inStorageCode &&
					this.formData.outQuantity &&
					this.formData.traceableCode) {
					const materialCode = this.formData.traceableCode.split('#')[0]
					queryMaterialName(materialCode).then(res => {
						this.formData.materialName = res.data.materielName
						// 将数据存入tableData
						this.tableData.push({
							codeType: this.formData.codeType,
							outStorageCode: this.formData.outStorageCode,
							inStorageCode: this.formData.inStorageCode,
							traceableCode: this.formData.traceableCode,
							outQuantity: this.formData.outQuantity,
							materielName: this.formData.materialName
						});

						// 保存inStorageCode和codeType的值
						const inStorageCode = this.formData.inStorageCode;
						const codeType = this.formData.codeType;

						// 重置表单数据
						this.formData = {
							codeType: codeType,
							inStorageCode: inStorageCode,
							outStorageCode: '',
							traceableCode: '',
							outQuantity: '',
							materialName: ''
						};
					})
				} else {
					this.$refs.toastRef.show({
						type: "error",
						message: "保存失败，请录入完整信息。"
					})
				}
			},
			confirm() {
				cpLocationMove(this.tableData).then(res => {
					if (res.data === true) {
						this.$refs.toastRef.show({
							type: "success",
							message: "移库成功。"
						});
						this.tableData = []
						this.formData.inStorageCode = ''
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		min-height: 100%;
		display: flex;
		flex-direction: column;
		background-color: #F1F3F7;
	}

	.form {
		flex: 1;
		margin: 15rpx;
		overflow: auto;
		padding: 5rpx 35rpx;
		border-radius: 30rpx;
		background-color: #ffffff;
	}

	.btn-row {
		display: flex;
		justify-content: space-between;
		margin: 20rpx 0;
	}

	.btn-submit {
		flex: 1;
		margin: 0 10rpx;
		padding: 0;
		font-size: 30rpx;
		line-height: 80rpx;
		background-color: #4F71FF;
		border-radius: 20rpx;
	}

	.uni-table {
		table-layout: fixed;
		width: 100%;
	}
</style>