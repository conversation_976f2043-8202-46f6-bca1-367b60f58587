<script>
export default {
  props: ["total"],

  methods: {},
};
</script>
<template>
  <view class="p-bottom">
    <!-- 占位 -->
    <view class="zw" />
    <view class="content">
      <slot />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.zw {
  height: 50px;
}
.content {
  height: 40px;
  margin-top: 20px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  height: 50px;
  background-color: #fff;
  border-top: 1px solid #000;
  .left {
    flex: 0.8;
    text-align: center;
    padding-right: 20px;
    border-right: 1px solid #000;
    margin-right: 10px;
  }
  .right {
    flex: 2;
    display: flex;
    flex-grow: abs($number: 3);
    justify-content: space-around;
    align-items: center;
  }
}
</style>
