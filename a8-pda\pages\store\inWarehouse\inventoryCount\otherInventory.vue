<template>
	<view>
		<view>
			<u-navbar title="计划外盘点" @leftClick="toPath()" bgColor="#4669ea" placeholder></u-navbar>
		</view>
		<view class="form">
			<uni-forms ref="baseFormRef" v-model="formData" :border="true" :label-width="100" :label-align="'left'">
				<uni-data-checkbox v-model="formData.codeType" :localdata="codeTypeOption"></uni-data-checkbox>

				<uni-forms-item label="库位" :border="true" name="storageLocation">
					<uni-easyinput v-model="formData.storageLocation" placeholder="请扫描目标库位码" :inputBorder="false"
						:placeholderStyle="`fontSize:12px;color:#999`" @change="storageLocationInputChange"
						@iconClick="storageLocationIconClick" prefixIcon="scan"></uni-easyinput>
				</uni-forms-item>
				<uni-forms-item label="追溯码" :border="true" name="traceableCode">
					<uni-easyinput v-model="formData.traceableCode" placeholder="请扫描出库产品追溯码" :inputBorder="false"
						:placeholderStyle="`fontSize:12px;color:#999`" @change="traceableCodeInputChange"
						@iconClick="traceableCodeIconClick" prefixIcon="scan"></uni-easyinput>
				</uni-forms-item>
				<uni-forms-item label="盘点数量" :border="true" name="physicalQuantity">
					<uni-easyinput type="number" :inputBorder="false" v-model="formData.physicalQuantity"
						:placeholderStyle="`fontSize:12px;color:#999`" @change="physicalQuantityInputChange"></uni-easyinput>
				</uni-forms-item>
			</uni-forms>

			<view class="btn-row">
				<button class="btn-submit" type="primary" @click="confirm">录入确认</button>
			</view>

			<view style="margin-top: 10rpx ;">
				<uni-table :border="true" stripe emptyText="暂无数据" style="border-radius: 20rpx;">
					<uni-tr>
						<uni-th width="70" align="center" style="font-size: 12px;">库位</uni-th>
						<uni-th width="90" align="center" style="font-size: 12px;">物料编码</uni-th>
						<uni-th width="70" align="center" style="font-size: 12px;">盘点数量</uni-th>
						<uni-th width="90" align="center" style="font-size: 12px;">物料描述</uni-th>
					</uni-tr>
					<uni-tr v-for="(item, index) in tableData" :key="index">
						<uni-td align="center">{{item.storageLocation}}</uni-td>
						<uni-td align="center" style="word-break: break-all;">{{item.materielCode}}</uni-td>
						<uni-td align="center">{{item.physicalQuantity}}</uni-td>
						<uni-td align="center">{{item.materielName}}</uni-td>
					</uni-tr>
				</uni-table>
			</view>
		</view>
		<u-toast ref="toastRef"></u-toast>
	</view>
	</view>
</template>

<script>
	import {
		GetItemListByInventoryNoAPI,
		InventoryCheckScanSubmitAPI
	} from "@/api/store/inWarehouse/inventoryCount"
	export default {
		data() {
			return {
				item: {},
				formData: {
					codeType: 1,
				},
				codeTypeOption: [{
					text: '批次码',
					value: 1
				}, {
					text: '序列号',
					value: 2,
					disable: true
				}, {
					text: '集成码',
					value: 3,
					disable: true
				}],
				tableData: [],
			};
		},
		onLoad: function(option) {
			const that = this;
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.on("itemData", function(data) {
				that.item = data;
			});
			console.log(this.item);
		},
		computed: {
			isOutboundQuantityEnabled() {
				return !!this.formData.traceableCode && !!this.formData.storageLocation;
			},
		},
		methods: {
			detailsData() {
				const inventoryNo = this.item.inventoryNo
				GetItemListByInventoryNoAPI(inventoryNo).then( res => {
							this.tableData = res.data.filter(item => item.itemType === "计划外盘点")
				})
			},
			//返回
			toPath() {
				uni.navigateBack({
					delta: 1,
				});
			},
			//库位
			storageLocationInputChange(val) {
				if (!val) return;
				this.formData.storageLocation = val
			},
			storageLocationIconClick() {
				const that = this
				uni.scanCode({
					onlyFromCamera: true,
					success: function(val) {
						that.$set(that.formData, "storageLocation", val.result)
					},
					fail() {
						that.$refs.toastRef.show({
							type: "error",
							message: "识别失败，请重新识别。"
						})
						that.$set(that.formData, "storageLocation", '')
					}
				})
			},
			//追溯码
			traceableCodeInputChange(val) {
				if (!val) return;
				this.formData.traceableCode = val;
			},
			traceableCodeIconClick() {
				const that = this;
				uni.scanCode({
					onlyFromCamera: true,
					success: function(val) {
						const code = val.result;
						that.$set(that.formData, "traceableCode", code);
					},
					fail() {
						that.$refs.toastRef.show({
							type: "error",
							message: "识别失败，请重新识别。"
						});
						that.$set(that.formData, "traceableCode", '');
					}
				});
			},
			//盘点数量
			physicalQuantityInputChange(val) {
				if (!val) return;
				// 验证是否为整数
				if (!/^[1-9]\d*$/.test(val)) {
					this.$refs.toastRef.show({
						type: "warning",
						message: "请输入正整数。"
					});
					this.formData.physicalQuantity = '';
					return;
				}
				this.formData.physicalQuantity = val
			},
			//录入确认
			confirm() {
				if (!this.formData.storageLocation || !this.formData.traceableCode || !this.formData.physicalQuantity) {
					this.$refs.toastRef.show({
						type: "warning",
						message: "请录入完整信息。"
					});
					return;
				}
				if (!/^[1-9]\d*$/.test(this.formData.physicalQuantity)) {
					this.$refs.toastRef.show({
						type: "warning",
						message: "请输入正整数。"
					});
					this.formData.physicalQuantity = '';
					return;
				}
				let params = {}
				params.isPlan = "计划外"
				params.num = this.formData.physicalQuantity
				params.traceableCode = this.formData.traceableCode
				params.codeType = this.formData.codeType
				params.inventoryNo = this.item.inventoryNo
				params.storageLocation = this.formData.storageLocation
				InventoryCheckScanSubmitAPI(params).then(res => {
					if (res.data == true) {
						this.$refs.toastRef.show({
							type: "success",
							message: "录入成功。"
						});
						this.detailsData();
						this.formData.traceableCode = ''
						this.formData.physicalQuantity = ''
					} else {
						this.formData.storageLocation = ''
						this.formData.traceableCode = ''
						this.formData.physicalQuantity = ''
					}
				})
			},
		}
	};
</script>

<style lang="scss" scoped>
	.lists {
		display: flex;
		justify-content: space-around;
		margin: 20rpx 0;
	}

	.tabs-bg {
		margin: 10rpx;
		border-radius: 10rpx;
		background: #fff;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		/* 让背景宽度100% */
		width: auto;
	}

	.form {
		flex: 1;
		margin: 10rpx;
		overflow: auto;
		padding: 5rpx 35rpx;
		border-radius: 10rpx;
		background-color: #ffffff;
	}
	.btn-row {
		display: flex;
		justify-content: space-between;
		margin: 20rpx 0;
	}
	.btn-submit {
		flex: 1;
		margin: 0 10rpx;
		padding: 0;
		font-size: 30rpx;
		line-height: 80rpx;
		background-color: #4F71FF;
		border-radius: 20rpx;
	}
</style>