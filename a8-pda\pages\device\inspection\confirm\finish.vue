<script>
	import navigationBar from "@/pages/components/navigation.vue";
	import {
		update,
		getConfirmItem,
		changeConfirmToFinish,
	} from "@/api/index/inspection";
	export default {
		component: {
			navigationBar,
		},
		data() {
			return {
				current: 0,
				virtualList: [],
				dataList: [],
				taskOption: {},
				resultTypeOption: [{
						text: "合格",
						value: "0",
					},
					{
						text: "不合格",
						value: "1",
					},
				],
				showFinishModal: false,
			};
		},

		onLoad(option) {
			this.taskOption = option;
		},

		methods: {
			query(pageNo, pageSize) {
				let params = this.taskOption;
				getConfirmItem(pageNo, pageSize, params)
					.then((res) => {
						// 处理图片字段
						this.virtualList = res.data.records.map(item => {
							let pictures = [];
							if (item.fillPicture) {
								try {
									const arr = JSON.parse(item.fillPicture);
									pictures = Array.isArray(arr) ? arr : [];
								} catch (e) {
									pictures = [];
								}
							}
							return {
								...item,
								pictures
							};
						});
						this.taskOption.taskId = res.data.records[0].taskId;
						this.$refs.paging.complete(this.virtualList);
					})
					.catch((res) => {
						this.$refs.paging.complete(false);
					});
			},
			virtualListChange(vList) {
				this.virtualList = vList;
			},
			//返回
			toPath() {
				uni.navigateBack({
					delta: 1,
				});
			},
			//结果类型映射
			getResultTypeLabel(value) {
				const option = this.resultTypeOption.find((item) => item.value === value);
				return option ? option.text : value;
			},
			//预览
			previewImg(imgList, idx) {
				uni.previewImage({
					urls: imgList,
					current: imgList[idx],
				});
			},
			//完成
			finishBtn() {
				this.showFinishModal = true;
			},
			onFinishConfirm() {
				changeConfirmToFinish(this.taskOption.taskId).then((res) => {
					if (res.code === 200) {
						this.showFinishModal = false;
						this.toPath();
					} else {
						this.$refs.toastRef.show({
							type: "error",
							message: "确认完成失败！",
						});
					}
				});
			},
		},
	};
</script>

<template>
	<z-paging ref="paging" v-model="dataList" @query="query" @virtualListChange="virtualListChange" use-virtual-list
		:force-close-inner-list="true" cell-height-mode="dynamic">
		<view>
			<u-navbar title="点检确认完成" @leftClick="toPath(1)" bgColor="#4669ea" placeholder></u-navbar>
		</view>

		<view :id="`zp-id-${item.zp_index}`" :key="item.zp_index" v-for="(item, index) in virtualList">
			<uni-card style="border-radius: 20rpx">
				<view class="uni-body">
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>设备编号：</text>
							<text class="blue-value">{{ taskOption.deviceNo }}</text>
						</view>
						<view class="card-body-col">
							<text>项目序号：</text>
							<text class="blue-value">{{ index + 1 }}</text>
						</view>
					</view>
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>检查部位：</text>
							<text>{{ item.checkLocation }}</text>
						</view>
						<view class="card-body-col">
							<text>点检内容：</text>
							<text>{{ item.inspectionContent }}</text>
						</view>
					</view>
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>检验标准：</text>
							<text>{{ item.inspectionStandard }}</text>
						</view>
						<view class="card-body-col">
							<text>检验工具：</text>
							<text>{{ item.inspectionTools }}</text>
						</view>
					</view>
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>最大值：</text>
							<text>{{ item.maxVal }}</text>
						</view>
						<view class="card-body-col">
							<text>最小值：</text>
							<text>{{ item.minVal }}</text>
						</view>
					</view>
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>检验方法：</text>
							<text>{{ item.inspectionMethod }}</text>
						</view>
						<view class="card-body-col">
							<text>结果类型：</text>
							<text>{{ getResultTypeLabel(item.resultType) }}</text>
						</view>
					</view>
					<view class="card-body-row2">
						<view class="card-body-col">
							<text>图片：</text>
							<view class="img-list">
								<image v-for="(img, idx) in item.pictures" :key="idx" :src="img" class="img-thumb"
									mode="aspectFill" @click="previewImg(item.pictures, idx)" />
							</view>
						</view>
					</view>
				</view>
			</uni-card>
		</view>
		<template #bottom>
			<view>
				<button class="finish-btn" @click="finishBtn">完 成</button>
			</view>
		</template>

		<!-- uView模态框 -->
		<u-modal :show="showFinishModal" title="警告" content="该任务将转为确认状态而且不可逆，请仔细检查是否完成所有任务，是否确认？" show-cancel-button
			confirm-text="确定" cancel-text="取消" confirm-color="#4669ea" @confirm="onFinishConfirm"
			@cancel="showFinishModal = false" />

		<u-toast ref="toastRef"></u-toast>
	</z-paging>
</template>

<style lang="scss" scoped>
	.img-list {
		display: flex;
		gap: 8rpx;
	}

	.img-thumb {
		width: 60rpx;
		height: 60rpx;
		border-radius: 8rpx;
		object-fit: cover;
		border: 1px solid #eee;
	}

	.uni-body {
		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
	}

	.card-body-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 15rpx;
		font-family: "PingFang-SC-Light";
	}

	.card-body-row2 {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10rpx;
	}

	.card-body-col {
		display: flex;
		align-items: center;
		gap: 4rpx;
		width: 48%;
		font-family: "PingFang-SC-Light";
	}

	.blue-value {
		color: #2e8ff4;
		font-weight: 500;
	}

	.finish-btn {
		width: 180rpx;
		height: 80rpx;
		background: #4669ea;
		color: #fff;
		border: none;
		border-radius: 30rpx;
		font-size: 30rpx;
		font-weight: bold;
		margin: 0rpx auto 20rpx auto;
		display: block;
	}

	.finish-btn:active {
		background: #3652b3;
	}
</style>