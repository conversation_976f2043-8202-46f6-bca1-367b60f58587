<script>
import { GetListAPI } from "@/api/index/index";
// #ifdef APP-PLUS
let pages = getCurrentPages();
let page = pages[pages.length - 1];
let currentWebview = page.$getAppWebview();
// #endif

export default {
  data() {
    return {
      headers: [
        {
          icon: "../../static/images/home/<USER>",
          text: "稽查填报",
          color: "#000000",
          size: "60px",
          path: "/pages/index/workOrder/index",
        },
        {
          icon: "../../static/images/home/<USER>",
          text: "任务列表",
          color: "#f0902d",
          size: "80px",
          path: "/pages/index/maintenance/index",
        },
        {
          icon: "../../static/images/home/<USER>",
          text: "统计分析",
          color: "#f0902d",
          size: "80px",
          path: "/pages/index/maintenance/index",
        },
      ],

      path: "../../static/images/开发中.png",
    };
  },

  async onLoad() {},
  onLaunch() {},
  async onShow() {},
  methods: {
    /**
     * 跳转
     * @param {*} val
     */
    toPath(val) {
      if (val == 1) {
        uni.navigateBack({
          delta: val,
        });
      } else {
        uni.navigateTo({
          url: val,
        });
      }
    },
  },
};
</script>

<template>
  <view class="index-page">
    <u-navbar placeholder titleStyle="color:#ffffff" @leftClick="toPath(1)">
    </u-navbar>

    <view class="box">
      <u--image
        :showLoading="true"
        :src="path"
        width="150px"
        height="150px"
      ></u--image>
      <view>努力开发中!</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.index-page {
  box-sizing: border-box;
  align-items: center;
  background-color: #fff;

  height: 100%;
}
.box {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  margin-top: 30%;
}
</style>
