import request from "@/utils/request"

// Sap列表
export function GetListAPI(current, size, params) {
  return request({
    url: '/wms/inventoryTransfer/queryInventoryTransfer',
    method: 'GET',
    params: {
        pageNum: current,
        pageSize: size,
    	...params
    }
  });
}

export function GetDetailsAPI(transferOrderNo) {
  return request({
    url: '/wms/inventoryTransfer/queryInventoryTransferItems',
    method: 'GET',
    params: { transferOrderNo }
  });
}

// 确认出库
export function AddDataAPI(data) {
  return request({
    url: '/wms/inventoryTransfer/appSaveData',
    method: 'POST',
    data
  });
}

//查询历史扫码
export function queryAllScan(transferOrderNo) {
  return request({
    url: '/wms/inventoryTransfer/queryAllScan',
    method: 'GET',
    params: { transferOrderNo }
  });
}

export function queryMaterielScan(transferOrderNo, materielCode) {
  return request({
    url: '/wms/inventoryTransfer/queryMaterielScan',
    method: 'GET',
    params: { transferOrderNo, materielCode }
  });
}
