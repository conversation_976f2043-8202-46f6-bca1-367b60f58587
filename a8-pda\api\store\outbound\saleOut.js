import request from "@/utils/request";

/**
 * 获取列表——SAP
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @constructor
 * @param params
 */
export function GetListForAPPFromSapAPI(current, size, params) {
  return request({
    url: "/wms/saleDelivery/listForAPP",
    method: "GET",
    params: {
      pageNum: current,
      pageSize: size,
      ...params,
    },
  });
}

/**
 * 获取明细——SAP
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @constructor
 * @param params
 */
export function GetItemsFromSapAPI(data) {
  return request({
    url: "/wms/saleDelivery/getItemsFromSAP/" + data,
    method: "GET",
  });
}

/**
 * 获取明细
 * @param deliveryNo
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function getItemsFromTable(deliveryNo) {
  return request({
    url: "/wms/saleDeliveryItem/getItemsFromTable/" + deliveryNo,
    method: "GET",
  });
}

/**
 * 根据明细实体获取明细
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function getItemFromTableByEntity(data) {
  return request({
    url: "/wms/saleDeliveryItem/getItemFromTableByEntity",
    method: "POST",
    data,
  });
}

/**
 * 获取批次推荐
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function getRecommendedBatches(data) {
  return request({
    url: "/wms/saleDelivery/getRecommendedBatches",
    method: "POST",
    data,
  });
}

/**
 * 获取追溯码历史
 * @param deliveryNo
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function getScanHistory(data) {
  return request({
    url: "/wms/saleDelivery/getScanHistory",
    method: "POST",
    data,
  });
}

/**
 * 根据物料编码获取有效期
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function queryMaterialName(materialCode) {
  return request({
    url: "/system/materiel/queryMaterialName",
    method: "GET",
    params: {
      materialCode,
    },
  });
}

/**
 * 扫描出库
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function deliverySubmitForAPP(data) {
  return request({
    url: "/wms/saleDelivery/deliverySubmitForAPP",
    method: "POST",
    data,
  });
}

/**
 * 撤销扫码
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function revokeScanForAPP(data) {
  return request({
    url: "/wms/saleDelivery/revokeScanForAPP",
    method: "POST",
    data,
  });
}

/**
 * 获取销售出库抬头信息
 * @param deliveryNo
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function getOrderInfo(data) {
  return request({
    url: "/wms/saleDelivery/getOrderInfo/" + data,
    method: "GET",
  });
}

/**
 * 保存物流信息
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function saveLogistics(data) {
  return request({
    url: "/wms/saleDelivery/saveLogistics",
    method: "POST",
    data,
  });
}

/**
 * 同步到SAP
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function toSap(data) {
  return request({
    url: '/wms/saleDelivery/toSap',
    method: 'POST',
    data
  });
}
