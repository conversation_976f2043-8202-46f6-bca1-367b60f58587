<template>
  <view class="layout-column">
    <camera
      :device-position="position"
      :flash="flash"
      @error="error"
      class="camera"
      :style="'height:' + cHei + 'px;'"
    >
      <cover-image
        class="close-img active"
        @click="close"
        src="/static/miniprogram/custom-camera/close.png"
      />
      <cover-view class="water-marker">
        <cover-view class="time">11:09</cover-view>
        <cover-view class="div"></cover-view>
        <cover-view class="date">2023-09-11</cover-view>
        <cover-view class="week">星期一</cover-view>
        <cover-view class="address">江西省南昌市东湖区广场北路2号</cover-view>
      </cover-view>
      <cover-image
        class="result"
        @click="handlePre"
        :src="photo"
        v-show="photo"
      />
    </camera>
    <view class="bottom layout-row less-center">
      <image
        :src="
          '/static/miniprogram/custom-camera/light-' +
          (flash == 'off' ? 'on' : 'off') +
          '.png'
        "
        :class="'light-img active ' + (position == 'front' ? 'hidden' : '')"
        @click="handleLight"
      />
      <view class="layout-row center cicle-outside">
        <view class="cicle-inside active" @click="doTakePhoto" />
      </view>
      <image
        src="/static/miniprogram/custom-camera/switch.png"
        class="switch-img active"
        @click="handlePosition"
      />
    </view>
    <canvas
      canvas-id="firstCanvas"
      class="canvas"
      :style="'width:' + canvasW + 'px;height: ' + canvasH + 'px'"
    />
  </view>
</template>

<script>
export default {
  data() {
    return {
      cHei: 0,
      position: "back",
      flash: "off",
      photo: "",
      canvasW: 0,
      canvasH: 0,
      fristTimedraw: true,
      //是否正在生成水印
      working: false,
    };
  },
  onLoad() {
    this.cHei = uni.getSystemInfoSync().windowHeight - uni.upx2px(300);
  },
  methods: {
    close() {
      uni.navigateBack({
        delta: 1,
      });
    },
    takePhoto() {
      const ctx = uni.createCameraContext();
      ctx.takePhoto({
        quality: "high",
        success: (res) => {
          console.log("takePhoto success", res);
          this.drawPhoto(res.tempImagePath);
        },
      });
    },
    doTakePhoto() {
      this.working = true;
      this.takePhoto();
      //这里真机上面第一次绘制水印图片可能需要很久，所以延迟500毫秒再执行一次
      if (this.fristTimedraw) {
        setTimeout(() => {
          this.takePhoto();
          this.fristTimedraw = false;
        }, 500);
      }
    },
    drawPhoto(path) {
      uni.getImageInfo({
        src: path,
        success: (res) => {
          let ctx = uni.createCanvasContext("firstCanvas");
          //设置画布宽高
          this.canvasW = res.width;
          this.canvasH = res.height;
          ctx.drawImage(path, 0, 0, res.width, res.height);
          //水印框的大小
          let w = 460;
          let h = 180;
          //水印框左上角坐标
          let x = 30;
          let y = res.height - 210;
          //圆角半径
          let r = 20;
          let time = "14:30";
          let date = "2023-09-12";
          let week = "星期二";
          let address = "江西省南昌市东湖区广场北路2号";
          ctx.beginPath();
          // 因为边缘描边存在锯齿，最好指定使用 transparent 填充
          ctx.setFillStyle("transparent");
          // 左上角
          ctx.arc(x + r, y + r, r, Math.PI, Math.PI * 1.5);

          // border-top
          ctx.moveTo(x + r, y);
          ctx.lineTo(x + w - r, y);
          ctx.lineTo(x + w, y + r);
          // 右上角
          ctx.arc(x + w - r, y + r, r, Math.PI * 1.5, Math.PI * 2);

          // border-right
          ctx.lineTo(x + w, y + h - r);
          ctx.lineTo(x + w - r, y + h);
          // 右下角
          ctx.arc(x + w - r, y + h - r, r, 0, Math.PI * 0.5);

          // border-bottom
          ctx.lineTo(x + r, y + h);
          ctx.lineTo(x, y + h - r);
          // 左下角
          ctx.arc(x + r, y + h - r, r, Math.PI * 0.5, Math.PI);

          // border-left
          ctx.lineTo(x, y + r);
          ctx.lineTo(x + r, y);

          // 这里是使用 fill 或者 stroke都可以
          ctx.fill();
          // ctx.stroke()
          ctx.closePath();
          // 剪切
          ctx.clip();
          ctx.setFillStyle("rgba(255, 255, 255, 0.2)");
          ctx.fillRect(x, y, w, h);

          //字体加粗真机不起作用?
          //ctx.font = "normal bold 50px Arial"
          ctx.setFontSize(55); // 设置字体大小
          ctx.setFillStyle("#FFFFFF"); // 设置颜色为白色
          ctx.fillText(time, x + 30, y + 70);

          let timeW = ctx.measureText(time).width;
          ctx.setFillStyle("#FFFF00"); // 设置颜色为
          ctx.fillRect(x + 30 + timeW + 30, y + 20, 8, 70);

          ctx.setFontSize(30); // 设置字体大小
          ctx.setFillStyle("#FFFFFF"); // 设置颜色为白色
          ctx.fillText(date, x + 30 + timeW + 30 + 50, y + 45);

          ctx.setFontSize(28); // 设置字体大小
          ctx.setFillStyle("#FFFFFF"); // 设置颜色为白色
          ctx.fillText(week, x + 30 + timeW + 30 + 50, y + 85);

          ctx.setFontSize(24); // 设置字体大小
          ctx.fillText(address, 60, y + 140);

          ctx.draw(false, () => {
            uni.showLoading({
              title: "正在生成水印照片",
            });
            uni.canvasToTempFilePath({
              canvasId: "firstCanvas",
              destWidth: this.canvasW * 2, //展示图片尺寸=画布尺寸1*像素比2
              destHeight: this.canvasH * 2,
              success: (res1) => {
                this.working = false;
                uni.hideLoading();
                ddthis.photo = res1.tempFilePath;
              },
            });
          });
        },
      });
    },
    savePhoto() {
      uni.saveImageToPhotosAlbum({
        filePath: ddthis.photo,
        success: (res) => {
          uni.showToast({
            title: "照片已保存到相册",
            icon: "none",
            duration: 2000,
          });
        },
      });
    },
    handleLight() {
      ddthis.flash = ddthis.flash == "on" ? "off" : "on";
    },
    handlePosition() {
      if (this.working) {
        return;
      }
      if (this.position == "back") {
        this.position = "front";
        //切换成前置摄像头关闭闪光灯
        ddthis.flash = "off";
      } else {
        this.position = "back";
      }
    },
    handlePre() {
      if (this.working) {
        return;
      }
      uni.previewImage({
        current: 0,
        urls: [ddthis.photo],
        success: (res) => {
          console.log(res);
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
page {
  width: 100%;
  height: 100%;
}
.camera {
  width: 100%;
  background: #999999;
}
.close-img {
  width: 48rpx;
  height: 48rpx;
  margin-top: 110rpx;
  margin-left: 40rpx;
}
.light-img {
  width: 48rpx;
  height: 48rpx;
}
.switch-img {
  width: 57rpx;
  height: 48rpx;
}
.bottom {
  width: 100%;
  height: 300rpx;
  background: black;
  justify-content: space-around;
}
.cicle-outside {
  width: 150rpx;
  height: 150rpx;
  border: 5rpx solid #fff;
  border-radius: 50%;
}
.cicle-inside {
  width: 130rpx;
  height: 130rpx;
  border-radius: 50%;
  background: #fff;
}
.hidden {
  visibility: hidden;
}
.water-marker {
  position: absolute;
  left: 30rpx;
  bottom: 30rpx;
  width: 430rpx;
  height: 180rpx;
  background: rgba($color: #ffffff, $alpha: 0.2);
  border-radius: 20rpx;
}
.time {
  font-size: 55rpx;
  color: white;
  position: absolute;
  top: 20rpx;
  left: 30rpx;
}
.div {
  border-radius: 3rpx;
  width: 8rpx;
  height: 70rpx;
  background: yellow;
  position: absolute;
  top: 20rpx;
  left: 200rpx;
}
.date {
  font-size: 28rpx;
  color: white;
  position: absolute;
  top: 20rpx;
  left: 240rpx;
  width: 180rpx;
}
.week {
  font-size: 28rpx;
  color: white;
  position: absolute;
  top: 60rpx;
  left: 240rpx;
}
.address {
  font-size: 24rpx;
  color: white;
  position: absolute;
  top: 120rpx;
  left: 30rpx;
  bottom: 30rpx;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-line;
}
.canvas {
  position: absolute;
  top: -999999rpx;
  width: 100%;
}
.result {
  width: 100rpx;
  height: 100rpx;
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
  background: white;
  border-radius: 50%;
}
</style>
