<script>
export default {
  props: ['text', 'keys'],
  data() {
    return {
      dictList: [
        {
          name: '未匹配成功',
          color: '#fff',
          bgColor: 'red',
        },
        {
          name: '待提交',
          color: '#239ceb',
          bgColor: '#e9f5fd',
        },
        {
          name: '待报修',
          color: '#f29d44',
          bgColor: '#fef4ea',
        },
        {
          name: '待维保',
          color: '#317fb2',
          bgColor: '#e9f1f7',
        },
        {
          name: '待派工',
          color: '#83bc41',
          bgColor: '#f0f7e8',
        },
        {
          name: '维保中',
          color: '#4de3e5',
          bgColor: '#eafbf6',
        },

        {
          name: '维修中',
          color: '#4de3e5',
          bgColor: '#eafbf6',
        },
        {
          name: '待审核',
          color: '#f0902c',
          bgColor: '#fef4ea',
        },
        {
          name: '未通过',
          color: '#cf0d23',
          bgColor: '#fae7e9',
        },
        {
          name: '未恢复',
          color: '#cf0d23',
          bgColor: '#fae7e9',
        },
        {
          name: '未确认',
          color: '#cf0d23',
          bgColor: '#fae7e9',
        },

        {
          name: '已报修',
          color: '#f0902c',
          bgColor: '#fef4ea',
        },
        {
          name: '已恢复',
          color: '#1a71a9',
          bgColor: '#e9f1f7',
        },
        {
          name: '已完成',
          color: '#747474',
          bgColor: '#ededed',
        },
        {
          name: '一般',
          color: '#FFF',
          bgColor: '#f0902d',
        },
        {
          name: '紧急',
          color: '#FFF',
          bgColor: '#d01227',
        },
      ],

      dataObj: {},
      textStr: '',
    }
  },

  methods: {
    /**
     * 自动匹配
     */
    autoMatch() {
      const find = this.dictList.find((t) => t.name == this.text)

      if (find) {
        this.dataObj = find
      } else {
        this.dataObj = this.dictList[0]
      }

      return this.dataObj
    },
  },

  mounted() {
    // console.log(this.text, 'texte')
  },
  watch: {
    text: {
      handler(n, o) {
        if (n) {
          this.$nextTick(() => {
            this.textStr = n
            const find = this.dictList.find((t) => t.name == this.textStr)
            if (find) {
              this.dataObj = find
            } else {
              this.dataObj = this.dictList[0]
            }
          })
        }
      },
      immediate: true,
    },
  },
}
</script>
<template>
  <view class="p-tag">
    <u-tag
      :style="{
        width: textStr
          ? textStr.length <= 2
            ? '2.5em'
            : textStr.length * 1 + 'em'
          : 'auto',
      }"
      size="mini"
      :text="textStr || '当前文字为空'"
      plain
      plainFill
      :bgColor="dataObj.bgColor"
      :color="dataObj.color"
      :borderColor="dataObj.bgColor"
    />

    <!-- <u-tag
      size="mini"
      text="待维保"
      plain
      plainFill
      bgColor="#e9f1f7"
      color="#317fb2"
      borderColor="#e9f1f7"
      v-else-if="props.status == 1"
    />
    <u-tag
      size="mini"
      text="维保中"
      plain
      plainFill
      bgColor="#eafbf6"
      color="#45d9b4"
      borderColor="#eafbf6"
      v-else-if="props.status == 2"
    />
    <u-tag
      size="mini"
      text="待派工"
      plain
      plainFill
      bgColor="#f0f7e8"
      color="#69ae18"
      borderColor="#f0f7e8"
      v-else-if="props.status == 3"
    />
    <u-tag
      size="mini"
      text="待审核"
      plain
      plainFill
      bgColor="#e9f5fd"
      color="#63b9f1"
      borderColor="#e9f5fd"
      v-else-if="props.status == 4"
    />
    <u-tag
      size="mini"
      text="未通过"
      plain
      plainFill
      bgColor="#fae7e9"
      color="#d32034"
      borderColor="#fae7e9"
      v-else-if="props.status == 5"
    />
    <u-tag
      size="mini"
      text="已完成"
      plain
      plainFill
      bgColor="#ededed"
      color="#767676"
      borderColor="#ededed"
      v-else
    /> -->
  </view>
</template>
