<script>
import { GetHistoryAPI } from '@/api/common'
export default {
  name: 'examineHistory',

  data() {
    return {
      /**
       * 开屏加载
       */
      loading: false,
      // 分页查询
      query: {
        pageNum: 1,
        pageSize: 5,
        //  是否数据隔离
      },
      total: 0,
      /**
       * 列表下拉上拉状态
       */
      status: 'nomore',
      /**
       * 历史记录 枚举
       */
      keys: [
        {
          label: '审核人',
          prop: 'auditUser',
        },
        {
          label: '评审状态',
          prop: 'auditState',
        },
        {
          label: '备注',
          prop: 'auditRemarks',
        },
      ],

      /**
       * 历史记录数据
       */
      tableData: [],
    }
  },
  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      this.tableData = []
      this.query.pageNum = 1
      await this.getList()
    } catch (err) {
    } finally {
      uni.stopPullDownRefresh()
    }
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  async onReachBottom() {
    if (this.status == 'nomore') return
    this.status = 'loading'
    this.query.pageNum = ++this.query.pageNum
    await this.getList()
  },
  /**
   * 首次进入加载页面
   * @param auditDoc 唯一字段
   * @param  perms 权限码
   * @param {*} option
   */
  async onLoad(option) {
    try {
      this.loading = true
      await this.getList(option.id)
    } catch (err) {
    } finally {
      this.loading = false
    }
  },
  methods: {
    /**
     * 跳转
     * @param {*} val
     */
    toPath(val) {
      if (val == 1) {
        uni.navigateBack({
          delta: val,
        })
      } else {
        // 通过页面通讯获取上传完的数据
        uni.$once('images', function (e) {
          this_.formValue.url = e.result
          this_.imageTotal = e.total
        })
        uni.navigateTo({
          url: val,
        })
      }
    },

    /**
     * 根据转入的keys和对象数据自动匹配
     */
    autoMatch(data) {
      this.dataArr = this.keys.map((t) => {
        if (t.prop === 'status') {
          switch (data[t.prop]) {
            case '1':
              data[t.prop] = '通过'
              break
            case '2':
              data[t.prop] = '未通过'
              break
          }
        }
        return {
          label: t.label,
          value: data[t.prop] || '未匹配到数据传入字段为=>' + t.prop,
        }
      })
      return this.dataArr
    },
    /**
     * 获取历史记录
     * @param auditDoc 唯一字段
     * @param  perms 权限码
     * @param {*} obj
     */
    async getList(obj) {
      const { data: result } = await GetHistoryAPI(obj)
      this.tableData = result
      this.total = 0
    },
  },
}
</script>
<template>
  <view class="examine-history">
    <u-navbar
      title="审核历史"
      @leftClick="toPath(1)"
      bgColor="#ffffff"
      placeholder
    >
    </u-navbar>
    <u-loading-page style="z-index: 9999" :loading="loading"></u-loading-page>
    <view class="examine-history-item" v-for="(t, i) in tableData" :key="i">
      <view class="flex-items">
        <view class="vertical-line"></view>
        <text class="ml-10">{{ t.createTime }}</text>
      </view>
      <p-card class="mt-10">
        <view class="content">
          <view class="left">
            <view class="content-item" v-for="(t, i) in autoMatch(t)" :key="i">
              <view
                class="label"
                :style="{ width: t.label.length * 1.2 + 'em' }"
                >{{ t.label }}:</view
              >
              <view class="value">
                {{ t.value }}
              </view>
            </view>
          </view>
          <view class="right">
            <view class="status" v-if="t.auditState == '评审通过'"
              >审核通过</view
            >
            <view class="status-error" v-else>审核未通过</view>
          </view>
        </view>
      </p-card>
    </view>
    <u-loadmore :status="status" />
  </view>
</template>
<style lang="scss" scoped>
.examine-history {
  .examine-history-item {
    margin-bottom: 20px;
    margin-top: 20px;
  }
  .flex-items {
    margin: 0 10px;
  }
  .content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      flex: 1;
      .content-item {
        width: 100%;
        display: flex;
        margin: 10px 0;
        .label {
          color: #747474;
        }
        .value {
          flex: 1;

          color: #000;
        }
      }
    }

    .right {
      flex: 0.3;

      .status,
      .status-error {
        width: 60px;
        height: 60px;
        color: #67ad15;
        border: 3px solid #67ad15;
        rotate: 45deg;
        padding: 5px;
        display: flex;
        font-size: 12px;
        align-items: center;
        border-radius: 50%;
        justify-content: center;
      }
      .status-error {
        color: #f56c6c;
        border-color: #f56c6c;
      }
    }
  }
}
</style>
