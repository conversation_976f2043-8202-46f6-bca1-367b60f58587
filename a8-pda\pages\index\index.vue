<script>
import { GetListAPI } from "@/api/index/index";
import { mapGetters } from "vuex";
import navigation from "../components/navigation.vue";
import { GetUseInfoAPI, NumtAPI } from "@/api/common.js";

// #ifdef APP-PLUS
let pages = getCurrentPages();
let page = pages[pages.length - 1];
let currentWebview = page.$getAppWebview();
// #endif

export default {
  computed: {
    ...mapGetters(["getRoleType"]),
  },
  components: {
    navigation,
  },
  data() {
    return {
      title: "未开始",
      text: "",
      partialResult: "...",
      result: "",
      valueWidth: "0px",
      submitForm: "",
      /**qms质量管理 */
      qms: [
        {
          icon: "../../static/images/IQC.png",
          text: "IQC",
          color: "#000000",
          size: "60px",
          path: "1",
        },
        {
          icon: "../../static/images/FAI.png",
          text: "FAI",
          color: "#000000",
          size: "60px",
          path: "1",
        },
        {
          icon: "../../static/images/IPQC.png",
          text: "IPQC",
          color: "#000000",
          size: "60px",
          path: "1",
        },
        {
          icon: "../../static/images/FQC.png",
          text: "FQC",
          color: "#000000",
          size: "60px",
          path: "1",
        },
        {
          icon: "../../static/images/OBA.png",
          text: "OBA",
          color: "#000000",
          size: "60px",
          path: "1",
        },
        {
          icon: "../../static/images/OQC.png",
          text: "OQC",
          color: "#000000",
          size: "60px",
          path: "1",
        },
      ],

      //wms仓储
      wms_warehouse: [
        {
          icon: "../../static/images/ruku.png",
          text: "入库管理",
          color: "#000000",
          size: "60px",
          path: "/pages/store/index",
        },
        {
          icon: "../../static/images/chuku.png",
          text: "出库管理",
          color: "#f0902d",
          size: "80px",
          path: "/pages/store/outbound/index",
        },
        {
          icon: "../../static/images/home/<USER>",
          text: "库内管理",
          color: "#f0902d",
          size: "80px",
          path: "/pages/store/inWarehouse/index",
        },
        {
          icon: "../../static/images/home/<USER>",
          text: "复入管理",
          color: "#f0902d",
          size: "80px",
          path: "1",
        },
        {
          icon: "../../static/images/fanxiu.png",
          text: "返修管理",
          color: "#f0902d",
          size: "80px",
          path: "1",
        },
        {
          icon: "../../static/images/tiaoma.png",
          text: "条码管理",
          color: "#f0902d",
          size: "80px",
          path: "1",
        },
      ],
      //mes
      mes: [
        {
          icon: "../../static/images/kaig.png",
          text: "生产开工",
          color: "#000000",
          size: "60px",
          path: "1",
        },
        {
          icon: "../../static/images/baohong.png",
          text: "生产报工",
          color: "#f0902d",
          size: "80px",
          path: "1",
        },
        {
          icon: "../../static/images/baoj.png",
          text: "生产报检",
          color: "#f0902d",
          size: "80px",
          path: "1",
        },
        {
          icon: "../../static/images/kc.png",
          text: "库存查询",
          color: "#f0902d",
          size: "80px",
          path: "1",
        },
      ],
      /*eap设备管理*/
      eap: [
        {
          icon: "../../static/images/dianjian.png",
          text: "设备点检",
          color: "#000000",
          size: "60px",
          path: "/pages/device/inspection/index",
        },
        {
          icon: "../../static/images/xuncha.png",
          text: "设备巡查",
          color: "#f0902d",
          size: "80px",
          path: "1",
        },
        {
          icon: "../../static/images/baoy.png",
          text: "设备保养",
          color: "#f0902d",
          size: "80px",
          path: "/pages/device/upkeep/index",
        },
        {
          icon: "../../static/images/sbwx.png",
          text: "设备维修",
          color: "#f0902d",
          size: "80px",
          path: "/pages/device/maintenance/index",
        },
        {
          icon: "../../static/images/sbtz.png",
          text: "设备台账",
          color: "#f0902d",
          size: "80px",
          path: "1",
        },
        {
          icon: "../../static/images/zsk.png",
          text: "知识库",
          color: "#f0902d",
          size: "80px",
          path: "1",
        },
      ],
      num: 0,
      num2: 0,
      status: "nomore",
      query: {
        pageNum: 1,
        pageSize: 10,
      },
      // 告警信息
      alarmInformation: [],
    };
  },

  async onLoad() {
    // #ifdef APP-PLUS
    // 监听语音识别事件
    plus.speech.addEventListener("start", this.ontStart, false);
    plus.speech.addEventListener("volumeChange", this.onVolumeChange, false);
    plus.speech.addEventListener("recognizing", this.onRecognizing, false);
    plus.speech.addEventListener("recognition", this.onRecognition, false);
    plus.speech.addEventListener("end", this.onEnd, false);
    // #endif
  },
  onLaunch() {},
  async onShow() {},
  methods: {
    /**取消 */
    custom() {
      console.log("取消");
    },
    /**搜索 */
    search() {
      console.log("搜索");
    },
    /**
          跳转页面
           */
    async click(v) {
      if (v == 1) {
        this.$refs.uToast.show({
          type: "error",
          title: "默认主题",
          message: "尚未开通服务！",
        });
      } else {
        if (v == "/pages/index/inspect/index") {
          const { data: res } = await GetUseInfoAPI();
          if (res.roleType != 1) {
            this.$refs.uToast.show({
              type: "error",
              title: "默认主题",
              message: "您当前不是安环员角色，无法进行填报！",
            });
            return;
          }

          uni.navigateTo({
            url: v,
          });
        } else {
          uni.navigateTo({
            url: v,
          });
        }
      }
    },
  },
};
</script>

<template>
  <view class="index-page">
    <!-- <navigationBar titles="首页"></navigationBar> -->
    <navigation>
      <u-input
        placeholder="搜索"
        v-model="submitForm"
        shape="circle"
        :customStyle="{ backgroundColor: '#fff' }"
      >
        <template slot="prefix">
          <u-icon name="search" color="#dbdbdb" size="28"></u-icon>
        </template>
        <template slot="suffix">
          <u-icon name="mic" color="#dbdbdb" size="20"></u-icon>
        </template>
      </u-input>
      <view class="custom">搜索</view>
      <view class="custom" @click="submitForm = ''">取消</view>
    </navigation>

    <view class="box">
      <view class="box-list">
        <view class="box-list-title"> WMS仓储管理</view>
        <view class="box-list-modulie-main">
          <view class="box-list-modulie">
            <u-grid :border="false" col="3">
              <u-grid-item
                v-for="(baseListItem, baseListIndex) in wms_warehouse"
                :key="baseListIndex"
                @click="click(baseListItem.path)"
                style="margin-bottom: 20rpx"
                class="grid"
              >
                <p-svg :src="baseListItem.icon" />

                <text class="grid-text">{{ baseListItem.text }}</text>
                <u-badge
                  :type="type"
                  class="badge"
                  max="99"
                  :value="num"
                  v-if="baseListItem.text == '自查记录'"
                ></u-badge>
              </u-grid-item>
            </u-grid>
          </view>
        </view>
      </view>
      <view class="box-list">
        <view class="box-list-title"> QMS质量管理</view>
        <view class="box-list-modulie-main">
          <view class="box-list-modulie">
            <u-grid :border="false" col="3">
              <u-grid-item
                v-for="(baseListItem, baseListIndex) in qms"
                :key="baseListIndex"
                @click="click(baseListItem.path)"
                style="margin-bottom: 20rpx"
                class="grid"
              >
                <p-svg :src="baseListItem.icon" />
                <text class="grid-text">{{ baseListItem.text }}</text>
                <u-badge
                  :type="type"
                  class="badge"
                  max="99"
                  :value="num2"
                  v-if="baseListItem.text == '任务列表'"
                ></u-badge>
              </u-grid-item>
            </u-grid>
          </view>
        </view>
      </view>
      <view class="box-list">
        <view class="box-list-title">MES制造执行</view>
        <view class="box-list-modulie-main">
          <view class="box-list-modulie">
            <u-grid :border="false" col="3">
              <u-grid-item
                v-for="(baseListItem, baseListIndex) in mes"
                :key="baseListIndex"
                @click="click(baseListItem.path)"
                style="margin-bottom: 20rpx"
              >
                <p-svg :src="baseListItem.icon" />
                <text class="grid-text">{{ baseListItem.text }}</text>
              </u-grid-item>
            </u-grid>
          </view>
        </view>
      </view>
      <view class="box-list">
        <view class="box-list-title"> EAP设备管理</view>
        <view class="box-list-modulie-main">
          <view class="box-list-modulie">
            <u-grid :border="false" col="3">
              <u-grid-item
                v-for="(baseListItem, baseListIndex) in eap"
                :key="baseListIndex"
                @click="click(baseListItem.path)"
                style="margin-bottom: 20rpx"
              >
                <p-svg :src="baseListItem.icon" />
                <text class="grid-text">{{ baseListItem.text }}</text>
              </u-grid-item>
            </u-grid>
          </view>
        </view>
      </view>
    </view>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<style lang="scss" scoped>
.index-page {
  //height: 100vh;
  // overflow: hidden;

  box-sizing: border-box;
  align-items: center;
  background-image: url("/static/images/home-bg2.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  height: 100%;
}

.box {
  height: calc(100vh - 95px);
  // padding: 40rpx;
  // height: 85%;
  padding-bottom: 110rpx;
  box-sizing: border-box;
  overflow-y: scroll;
}

.grid {
  position: relative;

  .badge {
    position: absolute;
    top: 0%;
    right: 15%;
  }
}

.custom {
  margin-left: 20rpx;
  color: #fff;
}

.box-list-modulie-main {
  //display: flex;
  padding: 20rpx 20rpx;
  width: 100%;

  .box-list-modulie {
    margin-right: 30rpx;
  }
}

.grid-text {
  margin-top: 10rpx;
  font-size: 30rpx;
}

/deep/ .u-navbar__content {
  background-color: rgba(0, 0, 0, 0) !important;
}

.box-list {
  width: 686rpx;
  //height: 230rpx;
  background: #ffffff;
  border-radius: 30rpx;
  margin-top: 40rpx !important;
  box-sizing: border-box;
  margin: 0 auto;
  padding: 25rpx 0 0 0;

  .box-list-title {
    font-family: PingFangSC-Regular;
    font-weight: 600;
    font-size: 35rpx;
    color: #222222;
    padding-left: 34rpx;
    box-sizing: border-box;
    position: relative;
  }

  .box-list-title:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0%;
    transform: translate(-0%, -50%);
    width: 10rpx;
    height: 30rpx;
    background-color: #4f71ff;
  }
}

.title-item {
  .content {
    display: flex;
    justify-content: center;
    margin-bottom: 15rpx;
  }

  .text {
    text-align: center;
    font-size: 14px;
  }
}

.dic {
  font-size: 16px;
}

.info-box {
  margin-top: 20px;
  overflow: auto;
  height: calc(100vh - 200px);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;

    .title {
      font-size: 20px;
    }

    .flex-between {
      width: 140px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        width: 120px;
        padding-left: 10px;
      }
    }
  }

  .content {
    word-wrap: break-word;
    margin-top: 5px;
    height: 100%;
    margin-bottom: 30px;
    color: #747474;
  }
}

.navbar_right {
  position: relative;

  .badge {
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>