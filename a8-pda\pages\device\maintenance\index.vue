<script>
import { GetUserAPI } from "@/api/index/maintenance";
import { mapGetters } from "vuex";
import { GetUseInfoAPI } from "@/api/common.js";
export default {
  computed: {
    ...mapGetters(["getInfo"]),
  },
  data() {
    return {
      userType: "", //角色类型 1是当人员为申请人，才有权限填报
      showSex: false,
    };
  },

  /*初始化*/
  async onShow() {
    // console.log(this.getInfo, "info");
    let { data: result } = await GetUseInfoAPI();
    const { data: res } = await GetUserAPI(result.user.userId);
    //判断当前人是否是申请人
    this.userType = res[result.user.userId] == "申请人" ? 1 : 2;
    this.$store.dispatch("getDeviceUser", res);
    console.log(this.userType, "this.userType");
  },
  methods: {
    /**
     * 跳转
     * @param {*} val
     */
    toPath(val, type) {
      if (val == 1) {
        uni.navigateBack({
          delta: val,
        });
      } else {
        uni.navigateTo({
          url: type ? val + "?" + `type=${type}` : val,
        });
      }
    },
  },
};
</script>

<template>
  <view class="page-main">
    <u-navbar
      title="设备维修"
      @leftClick="toPath(1)"
      bgColor="#ffffff"
      placeholder
    >
    </u-navbar>
    <u-cell-group class="cell">
      <!-- @click="toPath('/pages/device/maintenance/fillIn')" -->
      <u-cell
        title="设备维修填报"
        v-if="userType == 1"
        @click="showSex = true"
      ></u-cell>
      <u-cell
        @click="toPath('/pages/device/maintenance/task')"
        title="设备维修任务"
      ></u-cell>
    </u-cell-group>
    <u-action-sheet
      :show="showSex"
      :actions="[
        { name: '有设备编码维修', value: 1 },
        { name: '无设备编码维修', value: 2 },
      ]"
      title="请选择填报类型"
      @close="showSex = false"
      @select="
        (v) => {
          toPath('/pages/device/maintenance/fillIn', v.value);
        }
      "
    >
    </u-action-sheet>
  </view>
</template>

<style lang="scss" scoped>
.page-main {
  background-color: #f2f2f2;
}
.cell {
  margin-top: 20rpx;
  background-color: #fff;
}
</style>