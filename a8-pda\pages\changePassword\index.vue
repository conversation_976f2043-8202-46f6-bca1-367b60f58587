<script>
import { updatePwdAPI } from "@/api/common";

export default {
  data() {
    return {
      form: { oldPassword: "", newPassword: "", confirmPassword: "" },
      rules: {
        oldPassword: {
          type: "string",
          required: true,
          message: "请输入旧密码",
          trigger: ["blur", "change"],
        },
        newPassword: {
          type: "string",

          required: true,
          message: "请输入新密码",
          trigger: ["blur", "change"],
        },
        confirmPassword: {
          type: "string",
          required: true,
          message: "请再次输入新密码",
          validator: (rule, value, callback) => {
            console.log(value, "aaa");
            if (!value) {
              callback(new Error("请再次输入新密码"));
            } else if (!(value === this.form.newPassword)) {
              callback(new Error("两次密码不一致，请再次输入新密码"));
            } else {
              callback();
            }
          },
          trigger: ["blur", "change"],
        },
      },
    };
  },
  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {},

  async onLoad() {},

  methods: {
    /**提交 */
    async submit() {
      let res = await this.$refs.uForm.validate();
      if (!res) return;

      await updatePwdAPI(this.form);
      this.$refs.uToast.show({
        type: "success",
        title: "默认主题",
        message: "修改成功",
      });
    },
    /**
     * 跳转
     * @param {*} val
     */
    toPath(val) {
      console.log("val", val);
      if (val == 1) {
        uni.navigateBack({
          delta: val,
        });
      } else {
        uni.navigateTo({
          url: "/pages/index/environmental/index",
        });
      }
    },
  },
};
</script>

<template>
  <view class="page">
    <u-navbar
      title="修改密码"
      @leftClick="toPath(1)"
      bgColor="#5375ff"
      placeholder
      titleStyle="color:#ffffff"
    >
      <!-- <template #right> -->
      <!-- <view style="color: #fff; font-size: 16px" @click="submit()">完成</view> -->
      <!-- </template> -->
    </u-navbar>
    <u-form
      :model="form"
      ref="uForm"
      label-width="80"
      style="margin-top: 75px"
      :rules="rules"
    >
      <view style="padding: 5px">
        <view class="box">
          <u-form-item label="旧密码" prop="oldPassword">
            <u-input
              v-model="form.oldPassword"
              type="password"
              border="false"
              placeholder="请输入旧密码"
            />
          </u-form-item>
        </view>
        <view class="box">
          <u-form-item label="新密码" prop="newPassword">
            <u-input
              v-model="form.newPassword"
              border="false"
              type="password"
              placeholder="请输入新密码"
            />
          </u-form-item>
        </view>
        <view class="box">
          <u-form-item label="确认密码" prop="confirmPassword">
            <u-input
              v-model="form.confirmPassword"
              border="false"
              type="password"
              placeholder="请再次输入新密码"
            />
          </u-form-item>
        </view>
      </view>
      <u-button
        type="primary"
        shape="circle"
        color="#4f71ff"
        style="width: 80%"
        size="large"
        text="确认"
        class="button"
        @click="submit()"
      />
    </u-form>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<style lang="scss" scoped>
.box {
  border: 1px solid rgb(187, 187, 187);
  border-radius: 5px;
  padding: 0 5rpx;
  margin-bottom: 10rpx;
}
/deep/ .u-form-item__body {
  padding: 5rpx 0 !important;
}
</style>