<template>
	<z-paging ref="paging" v-model="dataList" @query="query" @virtualListChange="virtualListChange" use-virtual-list
		:force-close-inner-list="true" cell-height-mode="dynamic">
		<view>
			<u-navbar title="其他出库任务" @leftClick="toPath()" bgColor="#4669ea" placeholder></u-navbar>
		</view>
		
		<view class="tabs-bg">
			<uv-tabs :list="tabList" @click="changeTabs" :current="currentTabIndex"
				:itemStyle="{flex: 1, height: '44px'}"></uv-tabs>
		</view>

		<view style="display: flex;gap: 10rpx;align-items: center;margin: 10rpx;">
			<uni-easyinput style="flex: 4;" v-model="queryData.oid" placeholder="请输入出库单号搜索" border="surround"
				prefixIcon="scan" @iconClick="scanCode" @clear="clear">
			</uni-easyinput>

			<u-button style="flex: 1;" size="small" type="success" text="搜索" @click="search"></u-button>
		</view>

		<view :id="`zp-id-${item.zp_index}`" :key="item.zp_index" v-for="(item, index) in virtualList">
			<uni-card  style="border-radius: 20rpx">
				<template #title>
					<view
						style="display: flex;justify-content: center;align-items: center;margin-top: 5px;margin-left: 5px">
						<text>出库单号：{{item.oid}}</text>
						<u-button :customStyle="btnCustomStyle" type="primary" text="出库确认" 
							:disabled="item.outboundNum > 0" @click="synchronous(item)"></u-button>
						<u-button :customStyle="btnCustomStyle1" type="success" text="详情" @click="details(item)"></u-button>
					</view>

				</template>

				<view style="display: flex;justify-content: space-between">
					<text>单据状态: </text>
					<text>{{getStatusText(item.isState)}}</text>
				</view>
				<view style="display: flex;justify-content: space-between">
					<text>出库时间: </text>
					<text>{{item.outTime}}</text>
				</view>
				<view style="display: flex;justify-content: space-between">
					<text>单据总量: </text>
					<text>{{item.totalNum}}</text>
				</view>
				<view style="display: flex;justify-content: space-between">
					<text>待出库数量: </text>
					<text>{{item.outboundNum}}</text>
				</view>

			</uni-card>
		</view>
		<!-- 添加 toast 组件 -->
		<u-toast ref="toastRef"></u-toast>
	</z-paging>
</template>

<script>
	import {GetListAPI, FinishedAPI} from "@/api/store/outbound/otherOut"
	export default {
		data() {
			return {
				dataList: [],
				tabList: [
				  {
				    name: "其他出库",
				  },
				  {
				    name: "ERP其他出库",
					disabled: true
				  },
				],
				currentTabIndex: 0,
				queryData: {},
				virtualList: [],
				btnCustomStyle: {
					'margin-bottom': '15rpx',
					width: '70px',
					height: '30px',
					marginRight: '10rpx',
				},
				btnCustomStyle1: {
					'margin-bottom': '15rpx',
					width: '50px',
					height: '30px',
					marginLeft: '10rpx',
					marginRight: '10rpx'
				}
			};
		},
		methods: {
			changeTabs(item) {
				// this.currentTabIndex = item.index
				if (item.index === 1) {
					this.$refs.toastRef.show({
						type: "error",
						message: "暂未开通。"
					})
				}
			},
			synchronous(val) {
				if (val.isState == 3) {
					uni.showModal({
						title: '提示',
						content: '此出库任务还未开始出库，是否完成此单？',
						success: (res) => {
							if (res.confirm) {
								this.confirmInventory(val);
							}
						}
					});
					return;
				}
				this.confirmInventory(val);
			},
			confirmInventory(val) {
				FinishedAPI(val).then(res =>{
					if(res.code === 200) {
						this.$refs.toastRef.show({
							type: "success",
							message: "出库已完成。"
						})
						this.$refs.paging.reload();
					}
				})
			},
			getStatusText(status) {
				const statusMap = {
					3: '待出库',
					4: '出库中'
				}
				return statusMap[status] || status
			},
			search() {
				this.$refs.paging.reload();
			},
			clear() {
				this.queryData = [],
				this.$refs.paging.reload();
			},
			scanCode() {
				const that = this
				uni.scanCode({
					onlyFromCamera: true,
						success: function (res) {
							that.$set(that.queryData, "transferOrderNo", res.result);
						},
						fail() {
							that.$refs.toastRef.show({
								type: "error",
								message: "识别失败，请重新识别。"
							})
						}
				})
			},
			query(pageNo, pageSize) {
				let params = {}
				const {oid} = this.queryData
				if(oid) {
					params.oid = oid
				}
				GetListAPI(pageNo, pageSize, params).then(res => {
					this.virtualList = res.rows.filter(item => item.isState == 3 || item.isState == 4);
					this.$refs.paging.complete(res.rows.filter(item => item.isState == 3 || item.isState == 4));
				}).catch(res => {
					this.$refs.paging.complete(false);
				})
				
			},
			virtualListChange(vList) {
				this.virtualList = vList;
			},
			//返回
			toPath() {
				uni.navigateBack({
					delta: 1
				})
			},
			details(row) {
				uni.navigateTo({
							url: `/pages/store/outbound/otherOut/details`,
							success: function(res) {
								res.eventChannel.emit('rowData', row)
							}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.uni-body {
		font-size: 14px;
		font-weight: 400;
		line-height: 22px
	}
	
	.tabs-bg {
		margin: 10rpx;
		border-radius: 10rpx;
		background: #fff;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		/* 让背景宽度100% */
		width: auto;
	}

	.card-body-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 15rpx;
		font-family: 'PingFang-SC-Light';
	}

	.search {
		width: 93%;
		margin: 10rpx 10rpx 10rpx 12rpx;
		display: flex;
		flex-direction: column;
		gap: 15rpx;
		background-color: #ffffff;
		padding: 15rpx;
		border-radius: 20rpx;
		box-shadow: 0 10rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.search-row {
		display: flex;
		gap: 10rpx;
	}

</style>