{"id": "ui-echarts", "displayName": "ui-echarts 高性能可视化图表组件", "version": "1.0.5", "description": "基于百度开源的echarts封装的uni-app跨端可视化图表库, 兼容App、H5、小程序等平台", "keywords": ["ui-echarts", "echart", "echarts", "图表", "可视化"], "repository": "https://gitee.com/orelax/ui-echarts", "engines": {"HBuilderX": "^3.98"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "u"}, "App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}