<script>
import { GetListAPI } from "@/api/index/index";
import { mapGetters } from "vuex";
import navigation from "../components/navigation.vue";
import { GetUseInfoAPI, getMenuAPI } from "@/api/common.js";

// #ifdef APP-PLUS
let pages = getCurrentPages();
let page = pages[pages.length - 1];
let currentWebview = page.$getAppWebview();
// #endif

export default {
  computed: {
    ...mapGetters(["getRoleType"]),
  },
  components: {
    navigation,
  },
  data() {
    return {
      title: "未开始",
      text: "",
      partialResult: "...",
      result: "",
      valueWidth: "0px",
      submitForm: "",

      //wms仓储
      wms_warehouse: [
        {
          icon: "../../static/images/ruku.png",
          text: "入库配送",
          color: "#000000",
          size: "60px",
          // path: "/pages/components/yue",
          id: "1916386040494456833",
          path: "/pages/store/produce/index",
        },
        {
          icon: "../../static/images/chuku.png",
          text: "入库复核",
          color: "#f0902d",
          size: "80px",
          id: "1916387105310801922",
          path: "/pages/store/review/index",
        },
      ],

      num: 0,
      num2: 0,
      status: "nomore",
      query: {
        pageNum: 1,
        pageSize: 10,
      },
      // 告警信息
      alarmInformation: [],
    };
  },

  async onLoad() {},
  onLaunch() {},
  async onShow() {},
  methods: {
    /**
     * 返回
     */
    toPath(v) {
      if (v == 1) {
        uni.navigateBack({
          delta: v,
        });
      }
    },
  },
};
</script>

<template>
  <view class="index-page">
    <u-navbar
      title="生产入库"
      bgColor="#5375ff"
      placeholder
      titleStyle="color:#ffffff"
      height="44px"
      @leftClick="toPath(1)"
    >
    </u-navbar>
    <view class="box">
      <view class="box-list">
        <view class="box-list-title"> 入库管理</view>
        <view class="box-list-modulie-main">
          <view class="box-list-modulie">
            <u-grid :border="false" col="3">
              <u-grid-item
                v-for="(baseListItem, baseListIndex) in wms_warehouse"
                :key="baseListIndex"
                @click="menuPath(baseListItem)"
                style="margin-bottom: 20rpx"
                class="grid"
              >
                <p-svg :src="baseListItem.icon" />

                <text class="grid-text">{{ baseListItem.text }}</text>
                <u-badge
                  :type="type"
                  class="badge"
                  max="99"
                  :value="num"
                  v-if="baseListItem.text == '自查记录'"
                ></u-badge>
              </u-grid-item>
            </u-grid>
          </view>
        </view>
      </view>
    </view>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<style lang="scss" scoped>
.index-page {
  //height: 100vh;
  // overflow: hidden;

  box-sizing: border-box;
  align-items: center;
  background-image: url("/static/images/home-bg2.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  height: 100%;
}

.box {
  height: calc(100vh - 95px);
  // padding: 40rpx;
  // height: 85%;
  padding-bottom: 110rpx;
  box-sizing: border-box;
  overflow-y: scroll;
}

.grid {
  position: relative;

  .badge {
    position: absolute;
    top: 0%;
    right: 15%;
  }
}

.custom {
  margin-left: 20rpx;
  color: #fff;
}

.box-list-modulie-main {
  //display: flex;
  padding: 20rpx 20rpx;
  width: 100%;

  .box-list-modulie {
    margin-right: 30rpx;
  }
}

.grid-text {
  margin-top: 10rpx;
  font-size: 30rpx;
}

/deep/ .u-navbar__content {
  background-color: rgba(0, 0, 0, 0) !important;
}

.box-list {
  width: 686rpx;
  //height: 230rpx;
  background: #ffffff;
  border-radius: 30rpx;
  margin-top: 40rpx !important;
  box-sizing: border-box;
  margin: 0 auto;
  padding: 25rpx 0 0 0;

  .box-list-title {
    font-family: PingFangSC-Regular;
    font-weight: 600;
    font-size: 35rpx;
    color: #222222;
    padding-left: 34rpx;
    box-sizing: border-box;
    position: relative;
  }

  .box-list-title:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0%;
    transform: translate(-0%, -50%);
    width: 10rpx;
    height: 30rpx;
    background-color: #4f71ff;
  }
}

.title-item {
  .content {
    display: flex;
    justify-content: center;
    margin-bottom: 15rpx;
  }

  .text {
    text-align: center;
    font-size: 14px;
  }
}

.dic {
  font-size: 16px;
}

.info-box {
  margin-top: 20px;
  overflow: auto;
  height: calc(100vh - 200px);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;

    .title {
      font-size: 20px;
    }

    .flex-between {
      width: 140px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        width: 120px;
        padding-left: 10px;
      }
    }
  }

  .content {
    word-wrap: break-word;
    margin-top: 5px;
    height: 100%;
    margin-bottom: 30px;
    color: #747474;
  }
}

.navbar_right {
  position: relative;

  .badge {
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>