<script>
import { GetListAPI } from "@/api/index/message";
export default {
  name: "message",
  data() {
    return {
      tableData: [],
      query: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      status: "loadmore",
    };
  },
  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      this.tableData = [];
      this.query.pageNum = 1;
      await this.getList();
    } catch (err) {
    } finally {
      uni.stopPullDownRefresh();
    }
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  async onReachBottom() {
    if (this.status == "nomore") return;
    this.status = "loading";
    this.query.pageNum = ++this.query.pageNum;
    await this.getList();
  },
  onLoad() {
    this.getList();
  },
  methods: {
    /**
     * 获取列表
     */
    async getList() {
      const result = await GetListAPI(this.query);
      this.tableData = this.tableData.concat(result.rows);
      this.total = result.total;
      if (this.tableData.length >= this.total) this.status = "nomore";
    },
  },
};
</script>
<template>
  <view>
    <p-card
      class="mt-20"
      :title="t.createTime"
      v-for="(t, i) in tableData"
      :key="i"
    >
      <view class="content">{{ t.content }}</view>
    </p-card>
    <u-loadmore :status="status" />
    <p-bottom :total="total" />
  </view>
</template>

<style lang="scss" scoped>
.content {
  color: #747474;
}
</style>
