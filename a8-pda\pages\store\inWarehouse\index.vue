<script>
import navigation from "@/pages/components/navigation.vue";

export default {
  components: { navigation },
  data() {
    return {
      inWarehouse_menu: [
        {
          icon: "/static/images/kuweiyidong.png",
          text: "库位移动",
          path: "/pages/store/inWarehouse/locationMovement/index",
        },
        {
          icon: "/static/images/kucundiaobo.png",
          text: "库存调拨",
          path: "/pages/store/inWarehouse/stockTransfer/index",
        },
        {
          icon: "/static/images/kucunpandian.png",
          text: "库存盘点",
          path: "/pages/store/inWarehouse/inventoryCount/index",
        },
      ],
    };
  },
  methods: {
    click(v) {
      uni.navigateTo({ url: v });
    },
    toPath(v) {
      uni.navigateBack({ delta: v });
    },
  },
};
</script>

<template>
  <view class="index-page">
    <u-navbar
      title="库内管理"
      bgColor="#5375ff"
      placeholder
      titleStyle="color:#ffffff"
      height="44px"
      @leftClick="toPath(1)"
    />

    <view class="box">
      <view class="box-list">
        <view class="box-list-title">库内管理</view>
        <view class="box-list-modulie-main">
          <u-grid :border="false" col="3">
            <u-grid-item
              v-for="(item, index) in inWarehouse_menu"
              :key="index"
              @click="menuPath(item)"
              class="grid"
            >
              <p-svg :src="item.icon" />
              <text class="grid-text">{{ item.text }}</text>
            </u-grid-item>
          </u-grid>
        </view>
      </view>
    </view>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<style lang="scss" scoped>
.index-page {
  height: 100%;
  background-image: url("/static/images/home-bg2.jpg");
  background-size: cover;
  background-position: center;
}

.box {
  height: calc(100vh - 95px);
  padding-bottom: 110rpx;
}

.box-list {
  width: 686rpx;
  background: #ffffff;
  border-radius: 30rpx;
  margin: 40rpx auto 0;
  padding: 25rpx 0;

  &-title {
    font-weight: 600;
    font-size: 35rpx;
    color: #222222;
    padding-left: 34rpx;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 10rpx;
      height: 30rpx;
      background-color: #4f71ff;
    }
  }
}

.grid-text {
  margin-top: 10rpx;
  font-size: 30rpx;
  display: block;
  text-align: center;
}

.box-list-modulie-main {
  padding: 20rpx;
}
</style>